<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>合肥文旅助手</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #e3f2fd 0%, #f1f8e9 100%);
            color: #333;
        }
        
        .container {
            max-width: 414px;
            margin: 0 auto;
            background: white;
            min-height: 100vh;
            position: relative;
            overflow-x: hidden;
        }
        
        /* 顶部导航 */
        .header {
            background: linear-gradient(135deg, #2196f3, #4caf50);
            color: white;
            padding: 20px 16px 16px;
            position: relative;
        }
        
        .header h1 {
            font-size: 20px;
            font-weight: 600;
            text-align: center;
        }
        
        .location {
            display: flex;
            align-items: center;
            justify-content: center;
            margin-top: 8px;
            font-size: 14px;
            opacity: 0.9;
        }
        
        /* Banner轮播 */
        .banner {
            position: relative;
            height: 200px;
            overflow: hidden;
        }
        
        .banner-slider {
            display: flex;
            transition: transform 0.3s ease;
            height: 100%;
        }
        
        .banner-item {
            min-width: 100%;
            height: 100%;
            position: relative;
            background-size: cover;
            background-position: center;
        }
        
        .banner-item.slide1 {
            background-image: linear-gradient(rgba(0,0,0,0.3), rgba(0,0,0,0.3)), url('https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=800');
        }
        
        .banner-item.slide2 {
            background-image: linear-gradient(rgba(0,0,0,0.3), rgba(0,0,0,0.3)), url('https://images.unsplash.com/photo-1441974231531-c6227db76b6e?w=800');
        }
        
        .banner-item.slide3 {
            background-image: linear-gradient(rgba(0,0,0,0.3), rgba(0,0,0,0.3)), url('https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=800');
        }
        
        .banner-content {
            position: absolute;
            bottom: 20px;
            left: 20px;
            color: white;
        }
        
        .banner-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 4px;
        }
        
        .banner-subtitle {
            font-size: 14px;
            opacity: 0.9;
        }
        
        .banner-dots {
            position: absolute;
            bottom: 15px;
            right: 20px;
            display: flex;
            gap: 6px;
        }
        
        .dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: rgba(255,255,255,0.5);
            cursor: pointer;
            transition: background 0.3s;
        }
        
        .dot.active {
            background: white;
        }
        
        /* 智能服务 */
        .smart-services {
            padding: 20px 16px;
        }
        
        .section-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 16px;
            color: #2196f3;
        }
        
        .services-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 12px;
        }
        
        .service-item {
            background: white;
            border-radius: 12px;
            padding: 16px 8px;
            text-align: center;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            cursor: pointer;
            transition: transform 0.2s, box-shadow 0.2s;
        }
        
        .service-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }
        
        .service-icon {
            width: 40px;
            height: 40px;
            margin: 0 auto 8px;
            background: linear-gradient(135deg, #2196f3, #4caf50);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 20px;
        }
        
        .service-name {
            font-size: 14px;
            font-weight: 500;
            color: #333;
        }
        
        /* 合肥旅游圈 */
        .tourism-circle {
            padding: 20px 16px;
            background: #f8f9fa;
        }
        
        .map-container {
            background: white;
            border-radius: 12px;
            padding: 16px;
            margin-bottom: 16px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .map-filters {
            display: flex;
            gap: 8px;
            margin-bottom: 16px;
            overflow-x: auto;
            padding-bottom: 4px;
        }
        
        .filter-btn {
            padding: 6px 12px;
            border-radius: 20px;
            border: 1px solid #e0e0e0;
            background: white;
            font-size: 12px;
            white-space: nowrap;
            cursor: pointer;
            transition: all 0.2s;
        }
        
        .filter-btn.active {
            background: #2196f3;
            color: white;
            border-color: #2196f3;
        }
        
        .map-display {
            height: 200px;
            background: linear-gradient(135deg, #e8f5e8, #e3f2fd);
            border-radius: 8px;
            position: relative;
            overflow: hidden;
        }
        
        .map-point {
            position: absolute;
            width: 24px;
            height: 24px;
            background: #ff5722;
            border-radius: 50%;
            border: 2px solid white;
            cursor: pointer;
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0% { box-shadow: 0 0 0 0 rgba(255, 87, 34, 0.7); }
            70% { box-shadow: 0 0 0 10px rgba(255, 87, 34, 0); }
            100% { box-shadow: 0 0 0 0 rgba(255, 87, 34, 0); }
        }
        
        .point1 { top: 30%; left: 20%; }
        .point2 { top: 50%; left: 60%; }
        .point3 { top: 70%; left: 40%; }
        .point4 { top: 40%; left: 80%; }
        
        /* 推荐活动 */
        .activities {
            padding: 20px 16px;
        }
        
        .activities-scroll {
            display: flex;
            gap: 12px;
            overflow-x: auto;
            padding-bottom: 4px;
        }
        
        .activity-card {
            min-width: 200px;
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            cursor: pointer;
        }
        
        .activity-image {
            height: 100px;
            background-size: cover;
            background-position: center;
        }
        
        .activity-content {
            padding: 12px;
        }
        
        .activity-title {
            font-size: 14px;
            font-weight: 600;
            margin-bottom: 4px;
        }
        
        .activity-time {
            font-size: 12px;
            color: #666;
            margin-bottom: 4px;
        }
        
        .activity-status {
            font-size: 12px;
            padding: 2px 8px;
            border-radius: 10px;
            background: #4caf50;
            color: white;
            display: inline-block;
        }
        
        /* 底部导航 */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 100%;
            max-width: 414px;
            background: white;
            border-top: 1px solid #e0e0e0;
            display: flex;
            padding: 8px 0;
            z-index: 1000;
        }
        
        .nav-item {
            flex: 1;
            text-align: center;
            padding: 8px 4px;
            cursor: pointer;
            transition: color 0.2s;
        }
        
        .nav-item.active {
            color: #2196f3;
        }
        
        .nav-icon {
            font-size: 20px;
            margin-bottom: 4px;
        }
        
        .nav-text {
            font-size: 12px;
        }
        
        /* 页面内容 */
        .page-content {
            display: none;
            padding: 20px 16px 80px;
        }
        
        .page-content.active {
            display: block;
        }
        
        /* 隐藏滚动条 */
        .activities-scroll::-webkit-scrollbar,
        .map-filters::-webkit-scrollbar {
            display: none;
        }

        /* 探索页面样式 */
        .explore-tabs {
            display: flex;
            background: white;
            border-radius: 25px;
            padding: 4px;
            margin-bottom: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .tab-btn {
            flex: 1;
            padding: 8px 12px;
            text-align: center;
            border-radius: 20px;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.3s;
        }

        .tab-btn.active {
            background: #2196f3;
            color: white;
        }

        .explore-content {
            display: none;
        }

        .explore-content.active {
            display: block;
        }

        .filter-section {
            margin-bottom: 16px;
        }

        .filter-select {
            width: 100%;
            padding: 10px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            font-size: 14px;
        }

        .attraction-list {
            display: flex;
            flex-direction: column;
            gap: 16px;
        }

        .attraction-item {
            display: flex;
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            cursor: pointer;
            transition: transform 0.2s;
        }

        .attraction-item:hover {
            transform: translateY(-2px);
        }

        .attraction-image {
            width: 100px;
            height: 100px;
            background-size: cover;
            background-position: center;
        }

        .attraction-info {
            flex: 1;
            padding: 12px;
        }

        .attraction-info h3 {
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 4px;
        }

        .attraction-info p {
            font-size: 14px;
            color: #666;
            margin-bottom: 8px;
        }

        .attraction-meta {
            display: flex;
            gap: 12px;
            font-size: 12px;
            color: #888;
        }

        .food-list {
            display: flex;
            flex-direction: column;
            gap: 16px;
        }

        .food-item {
            display: flex;
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .food-image {
            width: 100px;
            height: 100px;
            background-size: cover;
            background-position: center;
        }

        .food-info {
            flex: 1;
            padding: 12px;
        }

        .food-info h3 {
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 8px;
        }

        .food-meta {
            display: flex;
            gap: 12px;
            font-size: 12px;
            color: #888;
            margin-bottom: 8px;
        }

        .food-tags {
            display: flex;
            gap: 6px;
        }

        .tag {
            padding: 2px 8px;
            background: #e3f2fd;
            color: #2196f3;
            border-radius: 10px;
            font-size: 11px;
        }

        /* 聊天界面样式 */
        .chat-container {
            height: calc(100vh - 200px);
            display: flex;
            flex-direction: column;
        }

        .chat-messages {
            flex: 1;
            overflow-y: auto;
            padding: 16px 0;
            margin-bottom: 16px;
        }

        .message {
            margin-bottom: 16px;
        }

        .bot-message .message-content {
            background: #f5f5f5;
            padding: 12px;
            border-radius: 12px;
            max-width: 80%;
        }

        .user-message {
            text-align: right;
        }

        .user-message .message-content {
            background: #2196f3;
            color: white;
            padding: 12px;
            border-radius: 12px;
            max-width: 80%;
            display: inline-block;
        }

        .quick-questions {
            margin-bottom: 16px;
        }

        .question-tags {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
        }

        .question-tag {
            padding: 8px 12px;
            background: white;
            border: 1px solid #e0e0e0;
            border-radius: 20px;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.2s;
        }

        .question-tag:hover {
            background: #2196f3;
            color: white;
            border-color: #2196f3;
        }

        .chat-input-container {
            display: flex;
            gap: 8px;
            padding: 16px;
            background: white;
            border-radius: 25px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .chat-input {
            flex: 1;
            border: none;
            outline: none;
            font-size: 14px;
        }

        .send-btn {
            padding: 8px 16px;
            background: #2196f3;
            color: white;
            border: none;
            border-radius: 20px;
            cursor: pointer;
            font-size: 14px;
        }

        /* 行程页面样式 */
        .itinerary-tabs {
            display: flex;
            background: white;
            border-radius: 25px;
            padding: 4px;
            margin-bottom: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .itinerary-content {
            display: none;
        }

        .itinerary-content.active {
            display: block;
        }

        .planning-card {
            background: white;
            border-radius: 12px;
            padding: 20px;
            text-align: center;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .planning-card h3 {
            font-size: 18px;
            margin-bottom: 8px;
            color: #2196f3;
        }

        .planning-card p {
            color: #666;
            margin-bottom: 16px;
        }

        .primary-btn {
            background: linear-gradient(135deg, #2196f3, #4caf50);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 25px;
            font-size: 14px;
            cursor: pointer;
            transition: transform 0.2s;
        }

        .primary-btn:hover {
            transform: translateY(-2px);
        }

        .trip-list {
            display: flex;
            flex-direction: column;
            gap: 16px;
        }

        .trip-item {
            background: white;
            border-radius: 12px;
            padding: 16px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .trip-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
        }

        .trip-header h3 {
            font-size: 16px;
            font-weight: 600;
        }

        .trip-status {
            padding: 4px 8px;
            border-radius: 10px;
            font-size: 12px;
            background: #4caf50;
            color: white;
        }

        .trip-status.completed {
            background: #9e9e9e;
        }

        .trip-info {
            margin-bottom: 12px;
            font-size: 14px;
            color: #666;
        }

        .trip-actions {
            display: flex;
            gap: 8px;
        }

        .trip-actions button {
            padding: 6px 12px;
            border: 1px solid #e0e0e0;
            background: white;
            border-radius: 6px;
            font-size: 12px;
            cursor: pointer;
        }

        /* 个人中心样式 */
        .profile-card {
            display: flex;
            align-items: center;
            background: white;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .profile-avatar {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            overflow: hidden;
            margin-right: 16px;
        }

        .profile-avatar img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .profile-info h3 {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 4px;
        }

        .profile-info p {
            color: #666;
            font-size: 14px;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 16px;
            margin-bottom: 20px;
        }

        .stat-item {
            background: white;
            border-radius: 12px;
            padding: 16px;
            text-align: center;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .stat-number {
            font-size: 24px;
            font-weight: 600;
            color: #2196f3;
            margin-bottom: 4px;
        }

        .stat-label {
            font-size: 12px;
            color: #666;
        }

        .menu-list {
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .menu-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 16px;
            border-bottom: 1px solid #f0f0f0;
            cursor: pointer;
            transition: background 0.2s;
        }

        .menu-item:last-child {
            border-bottom: none;
        }

        .menu-item:hover {
            background: #f8f9fa;
        }

        /* 模态框样式 */
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
            z-index: 2000;
        }

        .modal-content {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: white;
            border-radius: 12px;
            padding: 20px;
            max-width: 90%;
            max-height: 80%;
            overflow-y: auto;
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }

        .modal-title {
            font-size: 18px;
            font-weight: 600;
        }

        .close-btn {
            background: none;
            border: none;
            font-size: 24px;
            cursor: pointer;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 首页内容 -->
        <div id="home" class="page-content active">
            <!-- 顶部导航 -->
            <div class="header">
                <h1>合肥文旅助手</h1>
                <div class="location">
                    📍 合肥市 · 晴 22°C
                </div>
            </div>
            
            <!-- Banner轮播 -->
            <div class="banner">
                <div class="banner-slider" id="bannerSlider">
                    <div class="banner-item slide1">
                        <div class="banner-content">
                            <div class="banner-title">骆岗公园</div>
                            <div class="banner-subtitle">文化集中区 · 城市绿肺</div>
                        </div>
                    </div>
                    <div class="banner-item slide2">
                        <div class="banner-content">
                            <div class="banner-title">环巢湖片区</div>
                            <div class="banner-subtitle">生态休闲 · 湖光山色</div>
                        </div>
                    </div>
                    <div class="banner-item slide3">
                        <div class="banner-content">
                            <div class="banner-title">庐江山水</div>
                            <div class="banner-subtitle">康养度假 · 山水相依</div>
                        </div>
                    </div>
                </div>
                <div class="banner-dots">
                    <div class="dot active" onclick="goToSlide(0)"></div>
                    <div class="dot" onclick="goToSlide(1)"></div>
                    <div class="dot" onclick="goToSlide(2)"></div>
                </div>
            </div>
            
            <!-- 智能服务 -->
            <div class="smart-services">
                <div class="section-title">🤖 智能服务</div>
                <div class="services-grid">
                    <div class="service-item" onclick="openRouteService()">
                        <div class="service-icon">🗺️</div>
                        <div class="service-name">路径规划</div>
                    </div>
                    <div class="service-item" onclick="openGuideService()">
                        <div class="service-icon">🎧</div>
                        <div class="service-name">伴游导览</div>
                    </div>
                    <div class="service-item" onclick="openTravelNoteService()">
                        <div class="service-icon">📝</div>
                        <div class="service-name">游记生成</div>
                    </div>
                </div>
            </div>
            
            <!-- 合肥旅游圈 -->
            <div class="tourism-circle">
                <div class="section-title">🌟 合肥旅游圈</div>
                <div class="map-container">
                    <div class="map-filters">
                        <div class="filter-btn active" onclick="filterMap('all')">全部</div>
                        <div class="filter-btn" onclick="filterMap('scenic')">特色旅游</div>
                        <div class="filter-btn" onclick="filterMap('culture')">历史文化</div>
                        <div class="filter-btn" onclick="filterMap('family')">亲子科技</div>
                    </div>
                    <div class="map-display">
                        <div class="map-point point1" onclick="showSpotInfo('骆岗公园')" title="骆岗公园"></div>
                        <div class="map-point point2" onclick="showSpotInfo('环巢湖')" title="环巢湖"></div>
                        <div class="map-point point3" onclick="showSpotInfo('三河古镇')" title="三河古镇"></div>
                        <div class="map-point point4" onclick="showSpotInfo('科技馆')" title="合肥科技馆"></div>
                    </div>
                </div>
            </div>
            
            <!-- 推荐活动 -->
            <div class="activities">
                <div class="section-title">🎉 推荐活动</div>
                <div class="activities-scroll">
                    <div class="activity-card" onclick="showActivityDetail('春季花展')">
                        <div class="activity-image" style="background-image: url('https://images.unsplash.com/photo-1490750967868-88aa4486c946?w=400')"></div>
                        <div class="activity-content">
                            <div class="activity-title">春季花展</div>
                            <div class="activity-time">3月15日-4月30日</div>
                            <div class="activity-status">进行中</div>
                        </div>
                    </div>
                    <div class="activity-card" onclick="showActivityDetail('巢湖音乐节')">
                        <div class="activity-image" style="background-image: url('https://images.unsplash.com/photo-1493225457124-a3eb161ffa5f?w=400')"></div>
                        <div class="activity-content">
                            <div class="activity-title">巢湖音乐节</div>
                            <div class="activity-time">4月20日-4月22日</div>
                            <div class="activity-status">即将开始</div>
                        </div>
                    </div>
                    <div class="activity-card" onclick="showActivityDetail('科技体验周')">
                        <div class="activity-image" style="background-image: url('https://images.unsplash.com/photo-1518709268805-4e9042af2176?w=400')"></div>
                        <div class="activity-content">
                            <div class="activity-title">科技体验周</div>
                            <div class="activity-time">4月10日-4月16日</div>
                            <div class="activity-status">进行中</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 探索页面 -->
        <div id="explore" class="page-content">
            <div class="header">
                <h1>探索合肥</h1>
            </div>

            <div style="padding: 20px 16px;">
                <div class="explore-tabs">
                    <div class="tab-btn active" onclick="switchExploreTab('attractions', this)">景点</div>
                    <div class="tab-btn" onclick="switchExploreTab('food', this)">美食</div>
                    <div class="tab-btn" onclick="switchExploreTab('hotels', this)">酒店</div>
                    <div class="tab-btn" onclick="switchExploreTab('guides', this)">攻略</div>
                    <div class="tab-btn" onclick="switchExploreTab('notes', this)">游记</div>
                </div>

                <!-- 景点列表 -->
                <div id="attractions-content" class="explore-content active">
                    <div class="filter-section">
                        <select class="filter-select" onchange="filterAttractions(this.value)">
                            <option value="all">全部类型</option>
                            <option value="park">公园</option>
                            <option value="culture">文化</option>
                            <option value="tech">科技</option>
                        </select>
                    </div>

                    <div class="attraction-list">
                        <div class="attraction-item" onclick="showAttractionDetail('骆岗公园')">
                            <div class="attraction-image" style="background-image: url('https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=300')"></div>
                            <div class="attraction-info">
                                <h3>骆岗公园</h3>
                                <p>合肥最大的城市公园，集文化、生态于一体</p>
                                <div class="attraction-meta">
                                    <span>📍 2.5km</span>
                                    <span>💰 免费</span>
                                    <span>⭐ 4.8</span>
                                </div>
                            </div>
                        </div>

                        <div class="attraction-item" onclick="showAttractionDetail('三河古镇')">
                            <div class="attraction-image" style="background-image: url('https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=300')"></div>
                            <div class="attraction-info">
                                <h3>三河古镇</h3>
                                <p>千年古镇，江南水乡风情</p>
                                <div class="attraction-meta">
                                    <span>📍 45km</span>
                                    <span>💰 ¥80</span>
                                    <span>⭐ 4.6</span>
                                </div>
                            </div>
                        </div>

                        <div class="attraction-item" onclick="showAttractionDetail('合肥科技馆')">
                            <div class="attraction-image" style="background-image: url('https://images.unsplash.com/photo-1518709268805-4e9042af2176?w=300')"></div>
                            <div class="attraction-info">
                                <h3>合肥科技馆</h3>
                                <p>现代科技体验，适合亲子游玩</p>
                                <div class="attraction-meta">
                                    <span>📍 8km</span>
                                    <span>💰 ¥60</span>
                                    <span>⭐ 4.7</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 美食列表 -->
                <div id="food-content" class="explore-content">
                    <div class="food-list">
                        <div class="food-item">
                            <div class="food-image" style="background-image: url('https://images.unsplash.com/photo-1565299624946-b28f40a0ca4b?w=300')"></div>
                            <div class="food-info">
                                <h3>庐州烤鸭</h3>
                                <div class="food-meta">
                                    <span>🍽️ 徽菜</span>
                                    <span>📍 1.2km</span>
                                    <span>💰 ¥85/人</span>
                                </div>
                                <div class="food-tags">
                                    <span class="tag">招牌菜</span>
                                    <span class="tag">环境优雅</span>
                                </div>
                            </div>
                        </div>

                        <div class="food-item">
                            <div class="food-image" style="background-image: url('https://images.unsplash.com/photo-1504674900247-0877df9cc836?w=300')"></div>
                            <div class="food-info">
                                <h3>包河藕圆</h3>
                                <div class="food-meta">
                                    <span>🍽️ 小吃</span>
                                    <span>📍 0.8km</span>
                                    <span>💰 ¥25/人</span>
                                </div>
                                <div class="food-tags">
                                    <span class="tag">特色小吃</span>
                                    <span class="tag">老字号</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 伴游助手页面 -->
        <div id="assistant" class="page-content">
            <div class="header">
                <h1>伴游助手</h1>
            </div>

            <div style="padding: 20px 16px;">
                <div class="chat-container">
                    <div class="chat-messages" id="chatMessages">
                        <div class="message bot-message">
                            <div class="message-content">
                                您好！我是您的专属伴游助手，可以为您提供合肥旅游相关的任何帮助。您可以问我关于景点、美食、交通等问题。
                            </div>
                        </div>
                    </div>

                    <div class="quick-questions">
                        <div class="section-title">💡 常见问题</div>
                        <div class="question-tags">
                            <div class="question-tag" onclick="askQuestion('骆岗公园怎么去？')">骆岗公园怎么去？</div>
                            <div class="question-tag" onclick="askQuestion('合肥有什么特色美食？')">合肥有什么特色美食？</div>
                            <div class="question-tag" onclick="askQuestion('三河古镇门票多少钱？')">三河古镇门票多少钱？</div>
                            <div class="question-tag" onclick="askQuestion('适合亲子游的景点推荐')">适合亲子游的景点推荐</div>
                        </div>
                    </div>

                    <div class="chat-input-container">
                        <input type="text" id="chatInput" placeholder="请输入您的问题..." class="chat-input">
                        <button onclick="sendMessage()" class="send-btn">发送</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 行程页面 -->
        <div id="itinerary" class="page-content">
            <div class="header">
                <h1>我的行程</h1>
            </div>

            <div style="padding: 20px 16px;">
                <div class="itinerary-tabs">
                    <div class="tab-btn active" onclick="switchItineraryTab('planning', this)">行程规划</div>
                    <div class="tab-btn" onclick="switchItineraryTab('my-trips', this)">我的行程</div>
                </div>

                <div id="planning-content" class="itinerary-content active">
                    <div class="planning-card">
                        <h3>🗺️ 智能路径规划</h3>
                        <p>基于AI为您定制专属旅游路线</p>
                        <button class="primary-btn" onclick="openRouteService()">开始规划</button>
                    </div>
                </div>

                <div id="my-trips-content" class="itinerary-content">
                    <div class="trip-list">
                        <div class="trip-item">
                            <div class="trip-header">
                                <h3>合肥三日文化游</h3>
                                <span class="trip-status">进行中</span>
                            </div>
                            <div class="trip-info">
                                <p>📅 2024年4月15日 - 4月17日</p>
                                <p>👥 2人 · 💰 ¥1,200</p>
                            </div>
                            <div class="trip-actions">
                                <button onclick="viewTripDetail('trip1')">查看详情</button>
                                <button onclick="editTrip('trip1')">编辑</button>
                            </div>
                        </div>

                        <div class="trip-item">
                            <div class="trip-header">
                                <h3>环巢湖生态游</h3>
                                <span class="trip-status completed">已完成</span>
                            </div>
                            <div class="trip-info">
                                <p>📅 2024年3月20日 - 3月21日</p>
                                <p>👥 4人 · 💰 ¥800</p>
                            </div>
                            <div class="trip-actions">
                                <button onclick="viewTripDetail('trip2')">查看详情</button>
                                <button onclick="deleteTrip('trip2')">删除</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 我的页面 -->
        <div id="profile" class="page-content">
            <div class="header">
                <h1>个人中心</h1>
            </div>

            <div style="padding: 20px 16px;">
                <div class="profile-card">
                    <div class="profile-avatar">
                        <img src="https://images.unsplash.com/photo-1535713875002-d1d0cf377fde?w=100" alt="头像">
                    </div>
                    <div class="profile-info">
                        <h3>旅行达人</h3>
                        <p>合肥文旅爱好者</p>
                    </div>
                </div>

                <div class="stats-grid">
                    <div class="stat-item">
                        <div class="stat-number">12</div>
                        <div class="stat-label">游记数</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">2,580</div>
                        <div class="stat-label">积分</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">8</div>
                        <div class="stat-label">足迹</div>
                    </div>
                </div>

                <div class="menu-list">
                    <div class="menu-item" onclick="showMyNotes()">
                        <span>📝 我的游记</span>
                        <span>></span>
                    </div>
                    <div class="menu-item" onclick="showMyOrders()">
                        <span>🎫 我的订单</span>
                        <span>></span>
                    </div>
                    <div class="menu-item" onclick="showSettings()">
                        <span>⚙️ 设置</span>
                        <span>></span>
                    </div>
                </div>
            </div>
        </div>

        <!-- 底部导航 -->
        <div class="bottom-nav">
            <div class="nav-item active" onclick="switchPage('home', this)">
                <div class="nav-icon">🏠</div>
                <div class="nav-text">首页</div>
            </div>
            <div class="nav-item" onclick="switchPage('explore', this)">
                <div class="nav-icon">🔍</div>
                <div class="nav-text">探索</div>
            </div>
            <div class="nav-item" onclick="switchPage('assistant', this)">
                <div class="nav-icon">🤖</div>
                <div class="nav-text">伴游助手</div>
            </div>
            <div class="nav-item" onclick="switchPage('itinerary', this)">
                <div class="nav-icon">📅</div>
                <div class="nav-text">行程</div>
            </div>
            <div class="nav-item" onclick="switchPage('profile', this)">
                <div class="nav-icon">👤</div>
                <div class="nav-text">我的</div>
            </div>
        </div>
    </div>

    <!-- 模态框 -->
    <div id="modal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <div class="modal-title" id="modalTitle">详情</div>
                <button class="close-btn" onclick="closeModal()">&times;</button>
            </div>
            <div id="modalBody">
                <!-- 模态框内容将在这里动态加载 -->
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        let currentSlide = 0;
        let currentPage = 'home';
        let currentExploreTab = 'attractions';
        let currentItineraryTab = 'planning';

        // Banner轮播功能
        function goToSlide(index) {
            currentSlide = index;
            const slider = document.getElementById('bannerSlider');
            slider.style.transform = `translateX(-${index * 100}%)`;

            // 更新指示器
            document.querySelectorAll('.dot').forEach((dot, i) => {
                dot.classList.toggle('active', i === index);
            });
        }

        // 自动轮播
        setInterval(() => {
            currentSlide = (currentSlide + 1) % 3;
            goToSlide(currentSlide);
        }, 5000);

        // 页面切换功能
        function switchPage(page, element) {
            // 隐藏所有页面
            document.querySelectorAll('.page-content').forEach(p => {
                p.classList.remove('active');
            });

            // 显示目标页面
            document.getElementById(page).classList.add('active');

            // 更新导航状态
            document.querySelectorAll('.nav-item').forEach(item => {
                item.classList.remove('active');
            });

            // 如果有element参数，使用它；否则通过page查找对应的导航项
            if (element) {
                element.classList.add('active');
            } else {
                // 根据page找到对应的导航项
                const navItems = document.querySelectorAll('.nav-item');
                const pageIndex = ['home', 'explore', 'assistant', 'itinerary', 'profile'].indexOf(page);
                if (pageIndex >= 0 && navItems[pageIndex]) {
                    navItems[pageIndex].classList.add('active');
                }
            }

            currentPage = page;
        }

        // 智能服务功能
        function openRouteService() {
            showModal('路径规划', `
                <div style="text-align: center;">
                    <h3>🗺️ AI智能路径规划</h3>
                    <p>为您量身定制专属旅游路线</p>

                    <div style="margin: 20px 0;">
                        <h4>📍 推荐路线</h4>
                        <div style="display: flex; flex-direction: column; gap: 10px; margin: 10px 0;">
                            <button onclick="selectRoute('亲子游')" style="padding: 10px; border: 1px solid #e0e0e0; border-radius: 8px; background: white;">
                                👨‍👩‍👧‍👦 两日亲子游 - ¥680/人
                            </button>
                            <button onclick="selectRoute('文化游')" style="padding: 10px; border: 1px solid #e0e0e0; border-radius: 8px; background: white;">
                                🏛️ 三天文化游 - ¥520/人
                            </button>
                            <button onclick="selectRoute('生态游')" style="padding: 10px; border: 1px solid #e0e0e0; border-radius: 8px; background: white;">
                                🌿 两天生态休闲游 - ¥450/人
                            </button>
                            <button onclick="selectRoute('骆岗游')" style="padding: 10px; border: 1px solid #e0e0e0; border-radius: 8px; background: white;">
                                🏞️ 一天骆岗游 - ¥180/人
                            </button>
                            <button onclick="selectRoute('康养游')" style="padding: 10px; border: 1px solid #e0e0e0; border-radius: 8px; background: white;">
                                🧘‍♀️ 两天康养度假 - ¥580/人
                            </button>
                        </div>
                    </div>

                    <div style="margin: 20px 0;">
                        <h4>🤖 AI定制路线</h4>
                        <button onclick="openAIPlanning()" style="padding: 12px 24px; background: linear-gradient(135deg, #2196f3, #4caf50); color: white; border: none; border-radius: 25px;">
                            开始AI规划
                        </button>
                    </div>
                </div>
            `);
        }

        function openGuideService() {
            showModal('伴游导览', `
                <div style="text-align: center;">
                    <h3>🎧 智能伴游导览</h3>
                    <p>基于您的位置提供个性化导览服务</p>

                    <div style="margin: 20px 0;">
                        <div style="background: #f5f5f5; padding: 15px; border-radius: 8px; margin: 10px 0;">
                            📍 当前位置：合肥市政务区
                        </div>

                        <h4>附近景点</h4>
                        <div style="display: flex; flex-direction: column; gap: 8px; margin: 10px 0;">
                            <div onclick="playAudio('骆岗公园')" style="padding: 10px; border: 1px solid #e0e0e0; border-radius: 8px; background: white; cursor: pointer;">
                                🏞️ 骆岗公园 (2.5km) - 点击播放介绍
                            </div>
                            <div onclick="playAudio('合肥科技馆')" style="padding: 10px; border: 1px solid #e0e0e0; border-radius: 8px; background: white; cursor: pointer;">
                                🔬 合肥科技馆 (8km) - 点击播放介绍
                            </div>
                        </div>

                        <button onclick="startVoiceGuide()" style="padding: 12px 24px; background: #4caf50; color: white; border: none; border-radius: 25px; margin-top: 10px;">
                            🎤 语音交互
                        </button>
                    </div>
                </div>
            `);
        }

        function openTravelNoteService() {
            showModal('游记生成', `
                <div style="text-align: center;">
                    <h3>📝 AI游记生成</h3>
                    <p>基于您的旅行足迹自动生成精美游记</p>

                    <div style="margin: 20px 0;">
                        <div style="background: #f5f5f5; padding: 15px; border-radius: 8px; margin: 10px 0;">
                            <h4>选择行程</h4>
                            <select style="width: 100%; padding: 8px; border: 1px solid #e0e0e0; border-radius: 4px;">
                                <option>合肥三日文化游</option>
                                <option>环巢湖生态游</option>
                                <option>骆岗公园一日游</option>
                            </select>
                        </div>

                        <div style="background: #f5f5f5; padding: 15px; border-radius: 8px; margin: 10px 0;">
                            <h4>游记风格</h4>
                            <div style="display: flex; gap: 8px; justify-content: center;">
                                <button onclick="selectStyle('文艺')" style="padding: 6px 12px; border: 1px solid #e0e0e0; border-radius: 15px; background: white;">文艺风</button>
                                <button onclick="selectStyle('实用')" style="padding: 6px 12px; border: 1px solid #e0e0e0; border-radius: 15px; background: white;">实用攻略</button>
                                <button onclick="selectStyle('趣味')" style="padding: 6px 12px; border: 1px solid #e0e0e0; border-radius: 15px; background: white;">趣味日记</button>
                            </div>
                        </div>

                        <button onclick="generateTravelNote()" style="padding: 12px 24px; background: linear-gradient(135deg, #2196f3, #4caf50); color: white; border: none; border-radius: 25px;">
                            ✨ 生成游记
                        </button>
                    </div>
                </div>
            `);
        }

        // 地图功能
        function filterMap(type) {
            // 更新过滤按钮状态
            document.querySelectorAll('.filter-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            event.target.classList.add('active');

            // 这里可以添加地图点的显示/隐藏逻辑
            console.log('过滤地图类型:', type);
        }

        function showSpotInfo(spotName) {
            const spotInfo = {
                '骆岗公园': {
                    title: '骆岗公园',
                    description: '合肥最大的城市公园，集文化、生态、休闲于一体',
                    image: 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=400',
                    features: ['免费开放', '文化展览', '生态景观', '休闲娱乐'],
                    openTime: '全天开放',
                    address: '合肥市包河区骆岗街道'
                },
                '环巢湖': {
                    title: '环巢湖生态文化旅游区',
                    description: '中国五大淡水湖之一，生态休闲的绝佳去处',
                    image: 'https://images.unsplash.com/photo-1441974231531-c6227db76b6e?w=400',
                    features: ['湖光山色', '生态观光', '水上运动', '特色美食'],
                    openTime: '全天开放',
                    address: '合肥市环巢湖地区'
                },
                '三河古镇': {
                    title: '三河古镇',
                    description: '千年古镇，江南水乡风情浓郁',
                    image: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=400',
                    features: ['古建筑群', '水乡风情', '特色小吃', '历史文化'],
                    openTime: '8:00-17:30',
                    address: '合肥市肥西县三河镇'
                },
                '科技馆': {
                    title: '合肥科技馆',
                    description: '现代科技体验中心，适合亲子游玩',
                    image: 'https://images.unsplash.com/photo-1518709268805-4e9042af2176?w=400',
                    features: ['科技体验', '互动展览', '教育娱乐', '亲子活动'],
                    openTime: '9:00-17:00',
                    address: '合肥市蜀山区黄山路'
                }
            };

            const info = spotInfo[spotName];
            if (info) {
                showModal(info.title, `
                    <div>
                        <img src="${info.image}" style="width: 100%; height: 200px; object-fit: cover; border-radius: 8px; margin-bottom: 16px;">
                        <p style="color: #666; margin-bottom: 16px;">${info.description}</p>

                        <div style="margin-bottom: 16px;">
                            <h4>🌟 特色亮点</h4>
                            <div style="display: flex; flex-wrap: wrap; gap: 6px; margin-top: 8px;">
                                ${info.features.map(feature => `<span style="padding: 4px 8px; background: #e3f2fd; color: #2196f3; border-radius: 10px; font-size: 12px;">${feature}</span>`).join('')}
                            </div>
                        </div>

                        <div style="margin-bottom: 16px;">
                            <p><strong>🕒 开放时间：</strong>${info.openTime}</p>
                            <p><strong>📍 地址：</strong>${info.address}</p>
                        </div>

                        <div style="display: flex; gap: 8px; justify-content: center;">
                            <button onclick="addToItinerary('${spotName}')" style="padding: 8px 16px; background: #4caf50; color: white; border: none; border-radius: 20px;">加入行程</button>
                            <button onclick="showRoute('${spotName}')" style="padding: 8px 16px; background: #2196f3; color: white; border: none; border-radius: 20px;">查看路线</button>
                        </div>
                    </div>
                `);
            }
        }

        // 活动详情
        function showActivityDetail(activityName) {
            const activities = {
                '春季花展': {
                    title: '春季花展',
                    time: '2024年3月15日 - 4月30日',
                    location: '骆岗公园',
                    description: '春暖花开，百花齐放。骆岗公园春季花展邀您共赏春日美景。',
                    highlights: ['樱花大道', '郁金香花海', '摄影比赛', '花艺体验'],
                    price: '免费参观'
                },
                '巢湖音乐节': {
                    title: '巢湖音乐节',
                    time: '2024年4月20日 - 4月22日',
                    location: '环巢湖旅游区',
                    description: '湖光山色中的音乐盛宴，多位知名歌手倾情演出。',
                    highlights: ['明星演出', '湖边音乐', '美食集市', '露营体验'],
                    price: '¥180-680'
                },
                '科技体验周': {
                    title: '科技体验周',
                    time: '2024年4月10日 - 4月16日',
                    location: '合肥科技馆',
                    description: '最新科技成果展示，VR、AI等前沿技术体验。',
                    highlights: ['VR体验', 'AI互动', '机器人表演', '科普讲座'],
                    price: '¥30（学生半价）'
                }
            };

            const activity = activities[activityName];
            if (activity) {
                showModal(activity.title, `
                    <div>
                        <div style="background: linear-gradient(135deg, #2196f3, #4caf50); color: white; padding: 16px; border-radius: 8px; margin-bottom: 16px; text-align: center;">
                            <h3>${activity.title}</h3>
                            <p>📅 ${activity.time}</p>
                            <p>📍 ${activity.location}</p>
                        </div>

                        <p style="color: #666; margin-bottom: 16px;">${activity.description}</p>

                        <div style="margin-bottom: 16px;">
                            <h4>🌟 活动亮点</h4>
                            <div style="display: flex; flex-wrap: wrap; gap: 6px; margin-top: 8px;">
                                ${activity.highlights.map(highlight => `<span style="padding: 4px 8px; background: #e3f2fd; color: #2196f3; border-radius: 10px; font-size: 12px;">${highlight}</span>`).join('')}
                            </div>
                        </div>

                        <div style="margin-bottom: 16px;">
                            <p><strong>💰 价格：</strong>${activity.price}</p>
                        </div>

                        <div style="text-align: center;">
                            <button onclick="bookActivity('${activityName}')" style="padding: 12px 24px; background: linear-gradient(135deg, #2196f3, #4caf50); color: white; border: none; border-radius: 25px;">
                                立即预订
                            </button>
                        </div>
                    </div>
                `);
            }
        }

        // 探索页面功能
        function switchExploreTab(tab, element) {
            // 更新标签状态
            document.querySelectorAll('.explore-tabs .tab-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            if (element) {
                element.classList.add('active');
            }

            // 切换内容
            document.querySelectorAll('.explore-content').forEach(content => {
                content.classList.remove('active');
            });
            document.getElementById(tab + '-content').classList.add('active');

            currentExploreTab = tab;
        }

        function filterAttractions(type) {
            console.log('筛选景点类型:', type);
            // 这里可以添加实际的筛选逻辑
        }

        function showAttractionDetail(name) {
            showSpotInfo(name); // 复用景点信息显示功能
        }

        // 聊天功能
        function askQuestion(question) {
            const chatMessages = document.getElementById('chatMessages');

            // 添加用户消息
            const userMessage = document.createElement('div');
            userMessage.className = 'message user-message';
            userMessage.innerHTML = `<div class="message-content">${question}</div>`;
            chatMessages.appendChild(userMessage);

            // 模拟AI回复
            setTimeout(() => {
                const botMessage = document.createElement('div');
                botMessage.className = 'message bot-message';

                const responses = {
                    '骆岗公园怎么去？': '骆岗公园位于包河区，您可以乘坐地铁1号线到骆岗站，或者乘坐公交126路、150路等。从市中心打车约20分钟，费用大概25-35元。',
                    '合肥有什么特色美食？': '合肥特色美食有：庐州烤鸭、包河藕圆、三河米饺、吴山贡鹅、合肥龙虾等。推荐您去淮河路步行街或者罍街品尝地道美食。',
                    '三河古镇门票多少钱？': '三河古镇门票80元/人，学生票40元。包含古镇内主要景点：杨振宁旧居、刘同兴隆庄、鹤庐等。建议游玩时间3-4小时。',
                    '适合亲子游的景点推荐': '推荐亲子游景点：1.合肥科技馆（科普教育）2.合肥野生动物园（动物观赏）3.融创乐园（游乐设施）4.骆岗公园（户外活动）5.安徽博物院（历史文化）'
                };

                botMessage.innerHTML = `<div class="message-content">${responses[question] || '感谢您的提问，我会为您查找相关信息。如需更详细的帮助，请描述您的具体需求。'}</div>`;
                chatMessages.appendChild(botMessage);

                // 滚动到底部
                chatMessages.scrollTop = chatMessages.scrollHeight;
            }, 1000);

            // 滚动到底部
            chatMessages.scrollTop = chatMessages.scrollHeight;
        }

        function sendMessage() {
            const input = document.getElementById('chatInput');
            const message = input.value.trim();

            if (message) {
                askQuestion(message);
                input.value = '';
            }
        }

        // 回车发送消息
        document.addEventListener('DOMContentLoaded', function() {
            const chatInput = document.getElementById('chatInput');
            if (chatInput) {
                chatInput.addEventListener('keypress', function(e) {
                    if (e.key === 'Enter') {
                        sendMessage();
                    }
                });
            }
        });

        // 行程页面功能
        function switchItineraryTab(tab, element) {
            // 更新标签状态
            document.querySelectorAll('.itinerary-tabs .tab-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            if (element) {
                element.classList.add('active');
            }

            // 切换内容
            document.querySelectorAll('.itinerary-content').forEach(content => {
                content.classList.remove('active');
            });
            document.getElementById(tab + '-content').classList.add('active');

            currentItineraryTab = tab;
        }

        function viewTripDetail(tripId) {
            const trips = {
                'trip1': {
                    title: '合肥三日文化游',
                    dates: '2024年4月15日 - 4月17日',
                    people: '2人',
                    budget: '¥1,200',
                    status: '进行中',
                    itinerary: [
                        { day: 1, location: '三河古镇', time: '9:00-17:00', description: '游览古镇，品尝特色小吃' },
                        { day: 2, location: '包公园', time: '9:00-12:00', description: '了解包公文化' },
                        { day: 2, location: '安徽博物院', time: '14:00-17:00', description: '参观历史文物' },
                        { day: 3, location: '渡江战役纪念馆', time: '9:00-16:00', description: '学习革命历史' }
                    ]
                },
                'trip2': {
                    title: '环巢湖生态游',
                    dates: '2024年3月20日 - 3月21日',
                    people: '4人',
                    budget: '¥800',
                    status: '已完成',
                    itinerary: [
                        { day: 1, location: '巢湖湿地公园', time: '9:00-17:00', description: '观鸟赏景，生态体验' },
                        { day: 2, location: '姥山岛', time: '9:00-16:00', description: '乘船游湖，登岛观光' }
                    ]
                }
            };

            const trip = trips[tripId];
            if (trip) {
                showModal(trip.title, `
                    <div>
                        <div style="background: #f8f9fa; padding: 16px; border-radius: 8px; margin-bottom: 16px;">
                            <p><strong>📅 时间：</strong>${trip.dates}</p>
                            <p><strong>👥 人数：</strong>${trip.people}</p>
                            <p><strong>💰 预算：</strong>${trip.budget}</p>
                            <p><strong>📊 状态：</strong><span style="color: ${trip.status === '进行中' ? '#4caf50' : '#9e9e9e'}">${trip.status}</span></p>
                        </div>

                        <h4>📋 行程安排</h4>
                        <div style="margin-top: 12px;">
                            ${trip.itinerary.map(item => `
                                <div style="border-left: 3px solid #2196f3; padding-left: 12px; margin-bottom: 12px;">
                                    <div style="font-weight: 600;">第${item.day}天 - ${item.location}</div>
                                    <div style="color: #666; font-size: 14px;">${item.time}</div>
                                    <div style="color: #888; font-size: 13px; margin-top: 4px;">${item.description}</div>
                                </div>
                            `).join('')}
                        </div>

                        <div style="text-align: center; margin-top: 20px;">
                            <button onclick="editTrip('${tripId}')" style="padding: 8px 16px; background: #2196f3; color: white; border: none; border-radius: 20px; margin-right: 8px;">编辑行程</button>
                            <button onclick="shareTrip('${tripId}')" style="padding: 8px 16px; background: #4caf50; color: white; border: none; border-radius: 20px;">分享行程</button>
                        </div>
                    </div>
                `);
            }
        }

        function editTrip(tripId) {
            alert('编辑行程功能开发中...');
        }

        function deleteTrip(tripId) {
            if (confirm('确定要删除这个行程吗？')) {
                alert('行程已删除');
                // 这里可以添加实际的删除逻辑
            }
        }

        function shareTrip(tripId) {
            alert('行程分享链接已复制到剪贴板');
        }

        // 个人中心功能
        function showMyNotes() {
            showModal('我的游记', `
                <div>
                    <div style="display: flex; flex-direction: column; gap: 16px;">
                        <div style="display: flex; background: white; border-radius: 8px; overflow: hidden; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
                            <img src="https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=100" style="width: 80px; height: 80px; object-fit: cover;">
                            <div style="flex: 1; padding: 12px;">
                                <h4>春游骆岗公园</h4>
                                <p style="color: #666; font-size: 14px;">2024年3月25日</p>
                                <div style="display: flex; gap: 12px; font-size: 12px; color: #888;">
                                    <span>👁️ 1,234</span>
                                    <span>❤️ 89</span>
                                </div>
                            </div>
                        </div>

                        <div style="display: flex; background: white; border-radius: 8px; overflow: hidden; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
                            <img src="https://images.unsplash.com/photo-1441974231531-c6227db76b6e?w=100" style="width: 80px; height: 80px; object-fit: cover;">
                            <div style="flex: 1; padding: 12px;">
                                <h4>环巢湖生态之旅</h4>
                                <p style="color: #666; font-size: 14px;">2024年3月20日</p>
                                <div style="display: flex; gap: 12px; font-size: 12px; color: #888;">
                                    <span>👁️ 856</span>
                                    <span>❤️ 67</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div style="text-align: center; margin-top: 20px;">
                        <button onclick="createNewNote()" style="padding: 12px 24px; background: linear-gradient(135deg, #2196f3, #4caf50); color: white; border: none; border-radius: 25px;">
                            ✍️ 写新游记
                        </button>
                    </div>
                </div>
            `);
        }

        function showMyOrders() {
            showModal('我的订单', `
                <div>
                    <div style="display: flex; flex-direction: column; gap: 16px;">
                        <div style="background: white; border-radius: 8px; padding: 16px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
                            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 8px;">
                                <h4>骆岗无人巴士票</h4>
                                <span style="color: #4caf50; font-size: 12px;">已支付</span>
                            </div>
                            <p style="color: #666; font-size: 14px;">订单号：HF202404150001</p>
                            <p style="color: #666; font-size: 14px;">金额：¥20 × 2</p>
                            <div style="text-align: right; margin-top: 8px;">
                                <button onclick="showQRCode()" style="padding: 6px 12px; background: #2196f3; color: white; border: none; border-radius: 15px; font-size: 12px;">
                                    查看二维码
                                </button>
                            </div>
                        </div>

                        <div style="background: white; border-radius: 8px; padding: 16px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
                            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 8px;">
                                <h4>三河古镇门票</h4>
                                <span style="color: #9e9e9e; font-size: 12px;">已使用</span>
                            </div>
                            <p style="color: #666; font-size: 14px;">订单号：HF202403200002</p>
                            <p style="color: #666; font-size: 14px;">金额：¥80 × 2</p>
                        </div>
                    </div>
                </div>
            `);
        }

        function showSettings() {
            showModal('设置', `
                <div>
                    <div style="display: flex; flex-direction: column; gap: 16px;">
                        <div style="display: flex; justify-content: space-between; align-items: center; padding: 16px; background: white; border-radius: 8px;">
                            <span>消息通知</span>
                            <input type="checkbox" checked>
                        </div>

                        <div style="display: flex; justify-content: space-between; align-items: center; padding: 16px; background: white; border-radius: 8px;">
                            <span>位置服务</span>
                            <input type="checkbox" checked>
                        </div>

                        <div style="display: flex; justify-content: space-between; align-items: center; padding: 16px; background: white; border-radius: 8px;">
                            <span>语音播报</span>
                            <input type="checkbox">
                        </div>

                        <div onclick="clearCache()" style="padding: 16px; background: white; border-radius: 8px; text-align: center; cursor: pointer;">
                            清除缓存
                        </div>

                        <div onclick="about()" style="padding: 16px; background: white; border-radius: 8px; text-align: center; cursor: pointer;">
                            关于我们
                        </div>
                    </div>
                </div>
            `);
        }

        // 辅助功能
        function selectRoute(routeType) {
            alert(`您选择了${routeType}路线，正在为您展示详细信息...`);
            closeModal();
        }

        function openAIPlanning() {
            showModal('AI路径规划', `
                <div>
                    <h3 style="text-align: center; margin-bottom: 20px;">🤖 AI智能规划</h3>

                    <div style="display: flex; flex-direction: column; gap: 16px;">
                        <div>
                            <label style="display: block; margin-bottom: 8px; font-weight: 600;">游玩时间</label>
                            <select style="width: 100%; padding: 10px; border: 1px solid #e0e0e0; border-radius: 8px;">
                                <option>1天</option>
                                <option>2天</option>
                                <option>3天</option>
                                <option>4-7天</option>
                            </select>
                        </div>

                        <div>
                            <label style="display: block; margin-bottom: 8px; font-weight: 600;">人数</label>
                            <select style="width: 100%; padding: 10px; border: 1px solid #e0e0e0; border-radius: 8px;">
                                <option>1人</option>
                                <option>2人</option>
                                <option>3-5人</option>
                                <option>6人以上</option>
                            </select>
                        </div>

                        <div>
                            <label style="display: block; margin-bottom: 8px; font-weight: 600;">旅游类型</label>
                            <select style="width: 100%; padding: 10px; border: 1px solid #e0e0e0; border-radius: 8px;">
                                <option>文化历史</option>
                                <option>自然生态</option>
                                <option>亲子游乐</option>
                                <option>康养度假</option>
                                <option>美食体验</option>
                            </select>
                        </div>

                        <div>
                            <label style="display: block; margin-bottom: 8px; font-weight: 600;">预算范围</label>
                            <select style="width: 100%; padding: 10px; border: 1px solid #e0e0e0; border-radius: 8px;">
                                <option>500元以下</option>
                                <option>500-1000元</option>
                                <option>1000-2000元</option>
                                <option>2000元以上</option>
                            </select>
                        </div>

                        <div>
                            <label style="display: block; margin-bottom: 8px; font-weight: 600;">其他需求</label>
                            <textarea placeholder="请描述您的特殊需求..." style="width: 100%; padding: 10px; border: 1px solid #e0e0e0; border-radius: 8px; height: 80px; resize: none;"></textarea>
                        </div>
                    </div>

                    <div style="text-align: center; margin-top: 20px;">
                        <button onclick="generateAIRoute()" style="padding: 12px 24px; background: linear-gradient(135deg, #2196f3, #4caf50); color: white; border: none; border-radius: 25px;">
                            🚀 生成专属路线
                        </button>
                    </div>
                </div>
            `);
        }

        function generateAIRoute() {
            alert('AI正在为您生成专属路线，请稍候...');
            setTimeout(() => {
                alert('路线生成完成！已为您推荐最佳旅游方案。');
                closeModal();
            }, 2000);
        }

        function playAudio(spotName) {
            alert(`正在播放${spotName}的语音介绍...`);
        }

        function startVoiceGuide() {
            alert('语音导览已启动，请说出您想了解的内容...');
        }

        function selectStyle(style) {
            alert(`已选择${style}风格`);
        }

        function generateTravelNote() {
            alert('AI正在为您生成游记，请稍候...');
            setTimeout(() => {
                alert('游记生成完成！');
                closeModal();
            }, 2000);
        }

        function addToItinerary(spotName) {
            alert(`${spotName}已添加到您的行程中`);
        }

        function showRoute(spotName) {
            alert(`正在为您规划到${spotName}的最佳路线...`);
        }

        function bookActivity(activityName) {
            alert(`正在为您预订${activityName}...`);
        }

        function createNewNote() {
            alert('跳转到游记编辑页面...');
        }

        function showQRCode() {
            alert('显示订单二维码');
        }

        function clearCache() {
            alert('缓存已清除');
        }

        function about() {
            showModal('关于我们', `
                <div style="text-align: center;">
                    <h3>合肥文旅助手</h3>
                    <p style="color: #666; margin: 16px 0;">版本 1.0.0</p>
                    <p style="color: #666; margin: 16px 0;">
                        合肥文旅助手是您探索合肥的最佳伙伴，<br>
                        提供智能路径规划、伴游导览、游记生成等服务。
                    </p>
                    <p style="color: #666; margin: 16px 0;">
                        © 2024 合肥文旅助手<br>
                        技术支持：AI智能服务
                    </p>
                </div>
            `);
        }

        // 模态框功能
        function showModal(title, content) {
            document.getElementById('modalTitle').textContent = title;
            document.getElementById('modalBody').innerHTML = content;
            document.getElementById('modal').style.display = 'block';
        }

        function closeModal() {
            document.getElementById('modal').style.display = 'none';
        }

        // 点击模态框外部关闭
        document.addEventListener('DOMContentLoaded', function() {
            const modal = document.getElementById('modal');
            modal.addEventListener('click', function(e) {
                if (e.target === modal) {
                    closeModal();
                }
            });
        });

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('合肥文旅助手已加载完成');
        });
    </script>
</body>
</html>
