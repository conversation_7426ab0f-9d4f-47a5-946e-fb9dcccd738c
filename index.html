<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>合肥文旅助手</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #e3f2fd 0%, #f1f8e9 100%);
            color: #333;
        }
        
        .container {
            max-width: 414px;
            margin: 0 auto;
            background: white;
            min-height: 100vh;
            position: relative;
            overflow-x: hidden;
        }
        
        /* 顶部导航 */
        .header {
            background: linear-gradient(135deg, #2196f3, #4caf50);
            color: white;
            padding: 20px 16px 16px;
            position: relative;
        }
        
        .header h1 {
            font-size: 20px;
            font-weight: 600;
            text-align: center;
        }
        
        .location {
            display: flex;
            align-items: center;
            justify-content: center;
            margin-top: 8px;
            font-size: 14px;
            opacity: 0.9;
        }
        
        /* 首页头部样式 - 一行布局 */
        .home-header {
            position: relative;
            height: 60px;
            overflow: hidden;
            display: flex;
            align-items: center;
            justify-content: center;
            background: linear-gradient(135deg, #2196f3, #4caf50);
        }

        .header-bg {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg,
                rgba(33, 150, 243, 0.9),
                rgba(76, 175, 80, 0.9));
        }

        .header-content {
            position: relative;
            text-align: center;
            color: white;
            z-index: 2;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .app-title {
            font-size: 20px;
            font-weight: 700;
            text-shadow: 0 1px 2px rgba(0,0,0,0.2);
        }

        .app-subtitle {
            font-size: 12px;
            opacity: 0.9;
            font-weight: 500;
            text-shadow: 0 1px 2px rgba(0,0,0,0.2);
            margin: 0;
        }

        .separator {
            font-size: 16px;
            opacity: 0.7;
            font-weight: 300;
        }

        /* Banner轮播 - 紧凑版 */
        .banner {
            position: relative;
            height: 200px;
            overflow: hidden;
        }

        .banner.compact {
            height: 160px;
        }
        
        .banner-slider {
            display: flex;
            transition: transform 0.3s ease;
            height: 100%;
        }
        
        .banner-item {
            min-width: 100%;
            height: 100%;
            position: relative;
            background-size: cover;
            background-position: center;
        }
        
        .banner-item.slide1 {
            background-image: linear-gradient(rgba(0,0,0,0.3), rgba(0,0,0,0.3)), url('https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=800');
        }
        
        .banner-item.slide2 {
            background-image: linear-gradient(rgba(0,0,0,0.3), rgba(0,0,0,0.3)), url('https://images.unsplash.com/photo-1441974231531-c6227db76b6e?w=800');
        }
        
        .banner-item.slide3 {
            background-image: linear-gradient(rgba(0,0,0,0.3), rgba(0,0,0,0.3)), url('https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=800');
        }
        
        .banner-content {
            position: absolute;
            bottom: 20px;
            left: 20px;
            color: white;
        }
        
        .banner-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 4px;
        }
        
        .banner-subtitle {
            font-size: 14px;
            opacity: 0.9;
        }
        
        .banner-dots {
            position: absolute;
            bottom: 15px;
            right: 20px;
            display: flex;
            gap: 6px;
        }
        
        .dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: rgba(255,255,255,0.5);
            cursor: pointer;
            transition: background 0.3s;
        }
        
        .dot.active {
            background: white;
        }
        
        /* 智能服务 */
        .smart-services {
            padding: 20px 16px;
        }

        .smart-services.compact {
            padding: 12px 16px;
        }

        .section-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 16px;
            color: #2196f3;
        }

        .section-title-small {
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 10px;
            color: #2196f3;
        }

        .services-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 12px;
        }

        .services-grid-compact {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 8px;
        }

        .service-item {
            background: white;
            border-radius: 12px;
            padding: 16px 8px;
            text-align: center;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            cursor: pointer;
            transition: transform 0.2s, box-shadow 0.2s;
        }

        .service-item-compact {
            background: white;
            border-radius: 8px;
            padding: 10px 6px;
            text-align: center;
            box-shadow: 0 1px 4px rgba(0,0,0,0.1);
            cursor: pointer;
            transition: transform 0.2s, box-shadow 0.2s;
        }

        .service-item:hover,
        .service-item-compact:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }

        .service-icon {
            width: 40px;
            height: 40px;
            margin: 0 auto 8px;
            background: linear-gradient(135deg, #2196f3, #4caf50);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 20px;
        }

        .service-icon-small {
            width: 32px;
            height: 32px;
            margin: 0 auto 6px;
            background: linear-gradient(135deg, #2196f3, #4caf50);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 16px;
        }

        .service-name {
            font-size: 14px;
            font-weight: 500;
            color: #333;
        }

        .service-name-small {
            font-size: 12px;
            font-weight: 500;
            color: #333;
        }

        /* 智能服务渐变色版本 */
        .smart-services.gradient {
            padding: 16px;
            background: white;
        }

        .services-grid-gradient {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 12px;
        }

        .service-item-gradient {
            border-radius: 10px;
            padding: 12px 8px;
            text-align: center;
            cursor: pointer;
            transition: transform 0.2s, box-shadow 0.2s;
            color: white;
            position: relative;
            overflow: hidden;
        }

        .service-item-gradient:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }

        .service-item-gradient.route-planning {
            background: linear-gradient(135deg, #a8b5f0 0%, #b8a8d4 100%);
        }

        .service-item-gradient.guide-service {
            background: linear-gradient(135deg, #f5c2fc 0%, #f7a8b4 100%);
        }

        .service-item-gradient.travel-note {
            background: linear-gradient(135deg, #9dd6fe 0%, #7df4fe 100%);
        }

        .service-icon-gradient {
            width: 36px;
            height: 36px;
            margin: 0 auto 6px;
            background: rgba(255,255,255,0.25);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
            backdrop-filter: blur(10px);
        }

        .service-name-gradient {
            font-size: 13px;
            font-weight: 600;
            text-shadow: 0 1px 2px rgba(0,0,0,0.1);
        }
        
        /* 合肥旅游圈 */
        .tourism-circle {
            padding: 20px 16px;
            background: #f8f9fa;
        }

        .tourism-circle.compact {
            padding: 12px 16px;
        }

        .map-container {
            background: white;
            border-radius: 12px;
            padding: 16px;
            margin-bottom: 16px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .map-container-compact {
            background: white;
            border-radius: 8px;
            padding: 12px;
            margin-bottom: 12px;
            box-shadow: 0 1px 4px rgba(0,0,0,0.1);
        }

        .map-filters {
            display: flex;
            gap: 8px;
            margin-bottom: 16px;
            overflow-x: auto;
            padding-bottom: 4px;
        }

        .map-filters-compact {
            display: flex;
            gap: 6px;
            margin-bottom: 10px;
            overflow-x: auto;
            padding-bottom: 2px;
        }

        .filter-btn {
            padding: 6px 12px;
            border-radius: 20px;
            border: 1px solid #e0e0e0;
            background: white;
            font-size: 12px;
            white-space: nowrap;
            cursor: pointer;
            transition: all 0.2s;
        }

        .filter-btn-small {
            padding: 4px 8px;
            border-radius: 15px;
            border: 1px solid #e0e0e0;
            background: white;
            font-size: 10px;
            white-space: nowrap;
            cursor: pointer;
            transition: all 0.2s;
        }

        .filter-btn.active,
        .filter-btn-small.active {
            background: #2196f3;
            color: white;
            border-color: #2196f3;
        }

        .map-display {
            height: 200px;
            background: linear-gradient(135deg, #e8f5e8, #e3f2fd);
            border-radius: 8px;
            position: relative;
            overflow: hidden;
        }

        .map-display-compact {
            height: 140px;
            background: linear-gradient(135deg, #e8f5e8, #e3f2fd);
            border-radius: 6px;
            position: relative;
            overflow: hidden;
        }
        
        .map-point {
            position: absolute;
            width: 24px;
            height: 24px;
            background: #ff5722;
            border-radius: 50%;
            border: 2px solid white;
            cursor: pointer;
            animation: pulse 2s infinite;
        }

        .map-spot {
            position: absolute;
            cursor: pointer;
            text-align: center;
            transition: transform 0.2s;
        }

        .map-spot:hover {
            transform: scale(1.1);
        }

        .spot-image {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background-size: cover;
            background-position: center;
            border: 3px solid white;
            box-shadow: 0 2px 8px rgba(0,0,0,0.2);
            margin-bottom: 4px;
        }

        .spot-name {
            font-size: 12px;
            font-weight: 600;
            color: #333;
            background: rgba(255,255,255,0.9);
            padding: 2px 6px;
            border-radius: 10px;
            white-space: nowrap;
        }

        @keyframes pulse {
            0% { box-shadow: 0 0 0 0 rgba(255, 87, 34, 0.7); }
            70% { box-shadow: 0 0 0 10px rgba(255, 87, 34, 0); }
            100% { box-shadow: 0 0 0 0 rgba(255, 87, 34, 0); }
        }

        .spot1 { top: 30%; left: 15%; }
        .spot2 { top: 50%; left: 55%; }
        .spot3 { top: 70%; left: 35%; }
        .spot4 { top: 40%; left: 75%; }

        /* 紧凑版地图景点 */
        .map-spot-small {
            position: absolute;
            cursor: pointer;
            text-align: center;
            transition: transform 0.2s;
        }

        .map-spot-small:hover {
            transform: scale(1.1);
        }

        .spot-image-small {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background-size: cover;
            background-position: center;
            border: 2px solid white;
            box-shadow: 0 1px 4px rgba(0,0,0,0.2);
            margin-bottom: 2px;
        }

        .spot-name-small {
            font-size: 10px;
            font-weight: 600;
            color: #333;
            background: rgba(255,255,255,0.9);
            padding: 1px 4px;
            border-radius: 6px;
            white-space: nowrap;
        }
        
        /* 推荐活动 */
        .activities {
            padding: 20px 16px;
        }

        .activities.compact {
            padding: 12px 16px;
        }
        
        .activities-scroll {
            display: flex;
            gap: 12px;
            overflow-x: auto;
            padding-bottom: 4px;
        }

        .activities-scroll-compact {
            display: flex;
            gap: 8px;
            overflow-x: auto;
            padding-bottom: 2px;
        }
        
        .activity-card {
            min-width: 200px;
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            cursor: pointer;
            transition: transform 0.2s;
        }

        .activity-card-compact {
            min-width: 160px;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 1px 4px rgba(0,0,0,0.1);
            cursor: pointer;
            transition: transform 0.2s;
        }

        .activity-card:hover,
        .activity-card-compact:hover {
            transform: translateY(-2px);
        }
        
        .activity-image {
            height: 100px;
            background-size: cover;
            background-position: center;
        }

        .activity-image-compact {
            height: 70px;
            background-size: cover;
            background-position: center;
        }
        
        .activity-content {
            padding: 12px;
        }

        .activity-content-compact {
            padding: 8px;
        }
        
        .activity-title {
            font-size: 14px;
            font-weight: 600;
            margin-bottom: 4px;
            color: #333;
        }

        .activity-title-compact {
            font-size: 12px;
            font-weight: 600;
            margin-bottom: 3px;
            color: #333;
        }
        
        .activity-time {
            font-size: 12px;
            color: #666;
            margin-bottom: 4px;
        }

        .activity-time-compact {
            font-size: 10px;
            color: #666;
            margin-bottom: 3px;
        }
        
        .activity-status {
            font-size: 12px;
            padding: 2px 8px;
            border-radius: 10px;
            background: #4caf50;
            color: white;
            display: inline-block;
        }

        .activity-status-compact {
            font-size: 10px;
            padding: 1px 6px;
            border-radius: 8px;
            background: #4caf50;
            color: white;
            display: inline-block;
        }

        /* 游玩路线推荐样式 */
        .route-recommendations {
            padding: 20px 16px;
            background: #f8f9fa;
        }

        .route-recommendations.compact {
            padding: 12px 16px;
        }

        .routes-scroll {
            display: flex;
            gap: 12px;
            overflow-x: auto;
            padding-bottom: 4px;
        }

        .route-card {
            min-width: 220px;
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            cursor: pointer;
            transition: transform 0.2s;
            position: relative;
        }

        .route-card:hover {
            transform: translateY(-2px);
        }

        .route-image {
            height: 120px;
            background-size: cover;
            background-position: center;
        }

        .route-content {
            padding: 12px;
        }

        .route-content h3 {
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 4px;
            color: #333;
        }

        .route-content p {
            font-size: 13px;
            color: #666;
            margin-bottom: 8px;
            line-height: 1.4;
        }

        .route-meta {
            display: flex;
            gap: 8px;
            font-size: 11px;
            color: #888;
        }

        .route-meta span {
            background: #f0f0f0;
            padding: 2px 6px;
            border-radius: 8px;
        }

        .route-price-tag {
            position: absolute;
            top: 8px;
            right: 8px;
            background: linear-gradient(135deg, #ff5722, #ff9800);
            color: white;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 600;
        }
        
        /* 底部导航 */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 100%;
            max-width: 414px;
            background: white;
            border-top: 1px solid #e0e0e0;
            display: flex;
            padding: 8px 0;
            z-index: 1000;
        }
        
        .nav-item {
            flex: 1;
            text-align: center;
            padding: 8px 4px;
            cursor: pointer;
            transition: color 0.2s;
        }
        
        .nav-item.active {
            color: #2196f3;
        }
        
        .nav-icon {
            font-size: 20px;
            margin-bottom: 4px;
        }
        
        .nav-text {
            font-size: 12px;
        }
        
        /* 页面内容 */
        .page-content {
            display: none;
            padding: 20px 16px 80px;
        }
        
        .page-content.active {
            display: block;
        }
        
        /* 隐藏滚动条 */
        .activities-scroll::-webkit-scrollbar,
        .map-filters::-webkit-scrollbar {
            display: none;
        }

        /* 探索页面样式 */
        .explore-tabs {
            display: flex;
            background: white;
            border-radius: 25px;
            padding: 4px;
            margin-bottom: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .tab-btn {
            flex: 1;
            padding: 8px 12px;
            text-align: center;
            border-radius: 20px;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.3s;
        }

        .tab-btn.active {
            background: #2196f3;
            color: white;
        }

        .explore-content {
            display: none;
        }

        .explore-content.active {
            display: block;
        }

        .filter-section {
            margin-bottom: 16px;
        }

        .filter-select {
            width: 100%;
            padding: 10px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            font-size: 14px;
        }

        .attraction-list {
            display: flex;
            flex-direction: column;
            gap: 16px;
        }

        .attraction-item {
            display: flex;
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            cursor: pointer;
            transition: transform 0.2s;
        }

        .attraction-item:hover {
            transform: translateY(-2px);
        }

        .attraction-image {
            width: 100px;
            height: 100px;
            background-size: cover;
            background-position: center;
        }

        .attraction-info {
            flex: 1;
            padding: 12px;
        }

        .attraction-info h3 {
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 4px;
        }

        .attraction-info p {
            font-size: 14px;
            color: #666;
            margin-bottom: 8px;
        }

        .attraction-meta {
            display: flex;
            gap: 12px;
            font-size: 12px;
            color: #888;
        }

        .food-list {
            display: flex;
            flex-direction: column;
            gap: 16px;
        }

        .food-item {
            display: flex;
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .food-image {
            width: 100px;
            height: 100px;
            background-size: cover;
            background-position: center;
        }

        .food-info {
            flex: 1;
            padding: 12px;
        }

        .food-info h3 {
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 8px;
        }

        .food-meta {
            display: flex;
            gap: 12px;
            font-size: 12px;
            color: #888;
            margin-bottom: 8px;
        }

        .food-tags {
            display: flex;
            gap: 6px;
        }

        .tag {
            padding: 2px 8px;
            background: #e3f2fd;
            color: #2196f3;
            border-radius: 10px;
            font-size: 11px;
        }

        /* 聊天界面样式 */
        .chat-container {
            height: calc(100vh - 200px);
            display: flex;
            flex-direction: column;
        }

        .chat-messages {
            flex: 1;
            overflow-y: auto;
            padding: 16px 0;
            margin-bottom: 16px;
        }

        .message {
            margin-bottom: 16px;
        }

        .bot-message .message-content {
            background: #f5f5f5;
            padding: 12px;
            border-radius: 12px;
            max-width: 80%;
        }

        .user-message {
            text-align: right;
        }

        .user-message .message-content {
            background: #2196f3;
            color: white;
            padding: 12px;
            border-radius: 12px;
            max-width: 80%;
            display: inline-block;
        }

        .quick-questions {
            margin-bottom: 16px;
        }

        .question-tags {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
        }

        .question-tag {
            padding: 8px 12px;
            background: white;
            border: 1px solid #e0e0e0;
            border-radius: 20px;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.2s;
        }

        .question-tag:hover {
            background: #2196f3;
            color: white;
            border-color: #2196f3;
        }

        .chat-input-container {
            display: flex;
            gap: 8px;
            padding: 16px;
            background: white;
            border-radius: 25px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .chat-input {
            flex: 1;
            border: none;
            outline: none;
            font-size: 14px;
        }

        .send-btn {
            padding: 8px 16px;
            background: #2196f3;
            color: white;
            border: none;
            border-radius: 20px;
            cursor: pointer;
            font-size: 14px;
        }

        /* 行程页面样式 */
        .itinerary-tabs {
            display: flex;
            background: white;
            border-radius: 25px;
            padding: 4px;
            margin-bottom: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .itinerary-content {
            display: none;
        }

        .itinerary-content.active {
            display: block;
        }

        .planning-card {
            background: white;
            border-radius: 12px;
            padding: 20px;
            text-align: center;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .planning-card h3 {
            font-size: 18px;
            margin-bottom: 8px;
            color: #2196f3;
        }

        .planning-card p {
            color: #666;
            margin-bottom: 16px;
        }

        .primary-btn {
            background: linear-gradient(135deg, #2196f3, #4caf50);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 25px;
            font-size: 14px;
            cursor: pointer;
            transition: transform 0.2s;
        }

        .primary-btn:hover {
            transform: translateY(-2px);
        }

        .trip-list {
            display: flex;
            flex-direction: column;
            gap: 16px;
        }

        .trip-item {
            background: white;
            border-radius: 12px;
            padding: 16px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .trip-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
        }

        .trip-header h3 {
            font-size: 16px;
            font-weight: 600;
        }

        .trip-status {
            padding: 4px 8px;
            border-radius: 10px;
            font-size: 12px;
            background: #4caf50;
            color: white;
        }

        .trip-status.completed {
            background: #9e9e9e;
        }

        .trip-info {
            margin-bottom: 12px;
            font-size: 14px;
            color: #666;
        }

        .trip-actions {
            display: flex;
            gap: 8px;
        }

        .trip-actions button {
            padding: 6px 12px;
            border: 1px solid #e0e0e0;
            background: white;
            border-radius: 6px;
            font-size: 12px;
            cursor: pointer;
        }

        /* 个人中心样式 */
        .profile-card {
            display: flex;
            align-items: center;
            background: white;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .profile-avatar {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            overflow: hidden;
            margin-right: 16px;
        }

        .profile-avatar img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .profile-info h3 {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 4px;
        }

        .profile-info p {
            color: #666;
            font-size: 14px;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 16px;
            margin-bottom: 20px;
        }

        .stat-item {
            background: white;
            border-radius: 12px;
            padding: 16px;
            text-align: center;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .stat-number {
            font-size: 24px;
            font-weight: 600;
            color: #2196f3;
            margin-bottom: 4px;
        }

        .stat-label {
            font-size: 12px;
            color: #666;
        }

        .menu-list {
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .menu-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 16px;
            border-bottom: 1px solid #f0f0f0;
            cursor: pointer;
            transition: background 0.2s;
        }

        .menu-item:last-child {
            border-bottom: none;
        }

        .menu-item:hover {
            background: #f8f9fa;
        }

        /* 模态框样式 */
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
            z-index: 2000;
        }

        .modal-content {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: white;
            border-radius: 12px;
            padding: 20px;
            max-width: 90%;
            max-height: 80%;
            overflow-y: auto;
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }

        .modal-title {
            font-size: 18px;
            font-weight: 600;
        }

        .close-btn {
            background: none;
            border: none;
            font-size: 24px;
            cursor: pointer;
            color: #666;
        }

        /* 页面头部样式 */
        .page-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 16px;
            background: linear-gradient(135deg, #2196f3, #4caf50);
            color: white;
        }

        .back-btn {
            background: rgba(255,255,255,0.2);
            border: none;
            color: white;
            font-size: 18px;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .page-header h1 {
            font-size: 18px;
            font-weight: 600;
        }

        .header-actions {
            width: 40px;
        }

        /* 路径规划页面样式 */
        .route-planning-container {
            height: calc(100vh - 190px);
            display: flex;
            flex-direction: column;
        }

        .route-map-container {
            flex: 1;
            position: relative;
            background: linear-gradient(135deg, #e8f5e8, #e3f2fd);
        }

        .route-map {
            width: 100%;
            height: 100%;
            position: relative;
        }

        .map-background {
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, #e8f5e8, #e3f2fd);
            position: relative;
        }

        .route-display {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
        }

        .route-tabs {
            display: flex;
            background: white;
            border-top: 1px solid #e0e0e0;
        }

        .route-tab {
            flex: 1;
            padding: 12px;
            text-align: center;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            border-bottom: 2px solid transparent;
            transition: all 0.3s;
        }

        .route-tab.active {
            color: #2196f3;
            border-bottom-color: #2196f3;
            background: #f8f9fa;
        }

        .route-content {
            display: none;
            max-height: 300px;
            overflow-y: auto;
            background: white;
        }

        .route-content.active {
            display: block;
        }

        .recommended-list {
            padding: 16px;
        }

        .recommended-item {
            display: flex;
            align-items: center;
            padding: 12px;
            margin-bottom: 8px;
            background: #f8f9fa;
            border-radius: 8px;
            cursor: pointer;
            transition: background 0.2s;
        }

        .recommended-item:hover {
            background: #e3f2fd;
        }

        .route-icon {
            font-size: 24px;
            margin-right: 12px;
        }

        .route-info {
            flex: 1;
        }

        .route-info h3 {
            font-size: 14px;
            font-weight: 600;
            margin-bottom: 2px;
        }

        .route-info p {
            font-size: 12px;
            color: #666;
            margin-bottom: 4px;
        }

        .route-price {
            font-size: 12px;
            color: #2196f3;
            font-weight: 600;
        }

        /* AI规划表单样式 */
        .ai-planning-form {
            padding: 16px;
        }

        .form-group {
            margin-bottom: 16px;
        }

        .form-group label {
            display: block;
            font-size: 14px;
            font-weight: 600;
            margin-bottom: 6px;
            color: #333;
        }

        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 10px;
            border: 1px solid #e0e0e0;
            border-radius: 6px;
            font-size: 14px;
        }

        .form-group textarea {
            height: 80px;
            resize: none;
        }

        .generate-btn {
            width: 100%;
            padding: 12px;
            background: linear-gradient(135deg, #2196f3, #4caf50);
            color: white;
            border: none;
            border-radius: 25px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: transform 0.2s;
        }

        .generate-btn:hover {
            transform: translateY(-2px);
        }

        /* 悬浮按钮样式 - 位于banner下方 */
        .floating-buttons-top {
            display: flex;
            gap: 8px;
            justify-content: center;
            padding: 12px 16px;
            background: rgba(255,255,255,0.95);
            backdrop-filter: blur(10px);
            border-bottom: 1px solid #e0e0e0;
        }

        .float-btn {
            padding: 8px 16px;
            background: rgba(255,255,255,0.9);
            border: 1px solid #e0e0e0;
            border-radius: 20px;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.3s;
            backdrop-filter: blur(10px);
        }

        .float-btn.active {
            background: #2196f3;
            color: white;
            border-color: #2196f3;
        }

        /* 路线卡片横向展示 */
        .routes-horizontal-scroll {
            display: flex;
            gap: 12px;
            overflow-x: auto;
            padding: 16px;
            background: white;
        }

        .route-card-small {
            min-width: 120px;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            cursor: pointer;
            transition: transform 0.2s;
        }

        .route-card-small:hover {
            transform: translateY(-2px);
        }

        .route-card-image {
            height: 60px;
            background-size: cover;
            background-position: center;
        }

        .route-card-info {
            padding: 8px;
            text-align: center;
        }

        .route-card-info h4 {
            font-size: 12px;
            margin-bottom: 4px;
            color: #333;
        }

        .route-card-price {
            font-size: 11px;
            color: #ff5722;
            font-weight: 600;
        }

        /* 途经点横向展示 */
        .spots-horizontal-scroll {
            display: flex;
            gap: 12px;
            overflow-x: auto;
            padding: 16px;
            background: white;
        }

        .spot-card {
            min-width: 140px;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            cursor: pointer;
            transition: all 0.2s;
        }

        .spot-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }

        .spot-card.highlighted {
            border: 2px solid #2196f3;
            box-shadow: 0 4px 12px rgba(33,150,243,0.3);
        }

        .spot-image {
            height: 80px;
            background-size: cover;
            background-position: center;
        }

        .spot-info {
            padding: 8px;
        }

        .spot-info h5 {
            font-size: 12px;
            font-weight: 600;
            margin-bottom: 4px;
        }

        .spot-time {
            font-size: 10px;
            color: #666;
            margin-bottom: 4px;
        }

        .spot-price {
            font-size: 11px;
            color: #ff5722;
            font-weight: 600;
        }

        /* AI生成的途经点展示 */
        .generated-spots-scroll {
            display: flex;
            gap: 12px;
            overflow-x: auto;
            padding: 16px;
            background: white;
        }

        .generated-spot-card {
            min-width: 160px;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            cursor: pointer;
        }

        .generated-spot-image {
            height: 80px;
            background-size: cover;
            background-position: center;
        }

        .generated-spot-info {
            padding: 10px;
        }

        .generated-spot-info h5 {
            font-size: 13px;
            font-weight: 600;
            margin-bottom: 4px;
        }

        .generated-spot-time {
            font-size: 11px;
            color: #666;
            margin-bottom: 4px;
        }

        .generated-spot-price {
            font-size: 12px;
            color: #ff5722;
            font-weight: 600;
        }

        /* 底部操作按钮 */
        .route-actions-bottom {
            display: flex;
            gap: 8px;
            padding: 16px;
            background: white;
            border-top: 1px solid #e0e0e0;
        }

        .route-actions-bottom button {
            flex: 1;
            padding: 10px;
            border: 1px solid #e0e0e0;
            border-radius: 6px;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.2s;
        }

        .route-actions-bottom button:first-child {
            background: white;
            color: #666;
        }

        .route-actions-bottom button:last-child {
            background: #4caf50;
            color: white;
            border-color: #4caf50;
        }

        /* 选项按钮样式 */
        .option-buttons {
            display: flex;
            gap: 6px;
            flex-wrap: wrap;
        }

        .option-btn {
            padding: 6px 12px;
            border: 1px solid #e0e0e0;
            background: white;
            border-radius: 15px;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.2s;
        }

        .option-btn.active {
            background: #2196f3;
            color: white;
            border-color: #2196f3;
        }

        /* 路线详情面板样式 */
        .route-detail-panel {
            position: fixed;
            bottom: -100%;
            left: 0;
            width: 100%;
            max-width: 414px;
            background: white;
            border-radius: 16px 16px 0 0;
            box-shadow: 0 -4px 20px rgba(0,0,0,0.1);
            transition: bottom 0.3s ease;
            z-index: 1000;
        }

        .route-detail-panel.active {
            bottom: 0;
        }

        .panel-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 16px;
            border-bottom: 1px solid #e0e0e0;
        }

        .panel-header h3 {
            font-size: 16px;
            font-weight: 600;
        }

        .panel-content {
            max-height: 60vh;
            overflow-y: auto;
        }

        .route-summary {
            padding: 16px;
            background: #f8f9fa;
        }

        .route-spots-scroll {
            padding: 16px;
        }

        .route-spots {
            display: flex;
            gap: 12px;
            overflow-x: auto;
            padding-bottom: 8px;
        }

        .route-spot-card {
            min-width: 150px;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .spot-card-image {
            height: 80px;
            background-size: cover;
            background-position: center;
        }

        .spot-card-content {
            padding: 8px;
        }

        .spot-card-content h5 {
            font-size: 12px;
            font-weight: 600;
            margin-bottom: 4px;
        }

        .spot-card-tags {
            display: flex;
            gap: 4px;
            margin-bottom: 4px;
        }

        .spot-tag {
            font-size: 10px;
            padding: 2px 4px;
            background: #e3f2fd;
            color: #2196f3;
            border-radius: 4px;
        }

        .spot-price {
            font-size: 11px;
            color: #ff5722;
            font-weight: 600;
        }

        .route-actions {
            display: flex;
            gap: 8px;
            padding: 16px;
            border-top: 1px solid #e0e0e0;
        }

        .route-actions button {
            flex: 1;
            padding: 10px;
            border: 1px solid #e0e0e0;
            border-radius: 6px;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.2s;
        }

        .edit-route-btn {
            background: white;
            color: #666;
        }

        .save-route-btn {
            background: #4caf50;
            color: white;
            border-color: #4caf50;
        }

        .pay-route-btn {
            background: #ff5722;
            color: white;
            border-color: #ff5722;
        }

        /* 伴游导览页面样式 */
        .guide-container {
            height: calc(100vh - 140px);
            display: flex;
            flex-direction: column;
        }

        .guide-map-container {
            flex: 1;
            position: relative;
            background: linear-gradient(135deg, #e8f5e8, #e3f2fd);
        }

        .guide-map {
            width: 100%;
            height: 100%;
            position: relative;
        }

        .current-location {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            text-align: center;
        }

        .location-marker {
            font-size: 24px;
            animation: bounce 2s infinite;
        }

        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
            40% { transform: translateY(-10px); }
            60% { transform: translateY(-5px); }
        }

        .location-text {
            font-size: 12px;
            color: #333;
            background: rgba(255,255,255,0.9);
            padding: 2px 6px;
            border-radius: 10px;
            margin-top: 4px;
        }

        .nearby-spot {
            position: absolute;
            text-align: center;
            cursor: pointer;
        }

        .spot-marker {
            font-size: 20px;
            margin-bottom: 4px;
        }

        .spot-marker.blinking {
            animation: blink 1s infinite;
        }

        @keyframes blink {
            0%, 50% { opacity: 1; }
            51%, 100% { opacity: 0.3; }
        }

        .spot-label {
            font-size: 11px;
            background: rgba(255,255,255,0.9);
            padding: 2px 6px;
            border-radius: 8px;
            white-space: nowrap;
        }

        .spot-luogang { top: 20%; left: 20%; }
        .spot-tech { top: 30%; left: 70%; }
        .spot-chaohu { top: 70%; left: 40%; }

        /* 伴游导览紧凑样式 */
        .location-info-compact {
            padding: 8px 16px;
            background: #f8f9fa;
            border-bottom: 1px solid #e0e0e0;
            font-size: 12px;
            color: #666;
        }

        .nearby-attractions-compact {
            padding: 12px 16px;
        }

        .nearby-attractions-compact h4 {
            font-size: 13px;
            margin-bottom: 8px;
            color: #333;
        }

        .attractions-horizontal {
            display: flex;
            gap: 8px;
            overflow-x: auto;
            padding-bottom: 4px;
        }

        .attraction-compact {
            min-width: 70px;
            text-align: center;
            padding: 8px 4px;
            background: #f8f9fa;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.2s;
        }

        .attraction-compact:hover {
            background: #e3f2fd;
            transform: translateY(-1px);
        }

        .attraction-compact.highlighted {
            background: #2196f3;
            color: white;
        }

        .attraction-icon-small {
            font-size: 16px;
            margin-bottom: 2px;
        }

        .attraction-name {
            font-size: 10px;
            font-weight: 600;
            margin-bottom: 2px;
        }

        .attraction-distance {
            font-size: 9px;
            color: #888;
        }

        .attraction-compact.highlighted .attraction-distance {
            color: rgba(255,255,255,0.8);
        }

        /* 智能搜索样式 */
        .smart-search {
            padding: 12px 16px;
            border-top: 1px solid #e0e0e0;
        }

        .quick-tags {
            display: flex;
            gap: 6px;
            margin-bottom: 8px;
        }

        .quick-tag {
            padding: 4px 8px;
            background: #e3f2fd;
            color: #2196f3;
            border: none;
            border-radius: 12px;
            font-size: 10px;
            cursor: pointer;
            transition: all 0.2s;
        }

        .quick-tag:hover {
            background: #2196f3;
            color: white;
        }

        .search-input-container {
            display: flex;
            gap: 4px;
            align-items: center;
        }

        .search-input {
            flex: 1;
            padding: 8px 12px;
            border: 1px solid #e0e0e0;
            border-radius: 20px;
            font-size: 12px;
            outline: none;
        }

        .search-input:focus {
            border-color: #2196f3;
        }

        .input-mode-btn {
            width: 32px;
            height: 32px;
            border: 1px solid #e0e0e0;
            background: white;
            border-radius: 50%;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.2s;
        }

        .input-mode-btn.voice-mode {
            background: #4caf50;
            color: white;
            border-color: #4caf50;
        }

        .search-btn {
            padding: 8px 12px;
            background: #2196f3;
            color: white;
            border: none;
            border-radius: 15px;
            font-size: 11px;
            cursor: pointer;
        }

        .guide-panel {
            background: white;
            max-height: 50vh;
            overflow-y: auto;
            border-top: 1px solid #e0e0e0;
        }

        .location-info {
            padding: 16px;
            background: #f8f9fa;
            border-bottom: 1px solid #e0e0e0;
        }

        .location-info h3 {
            font-size: 16px;
            margin-bottom: 4px;
        }

        .location-info p {
            font-size: 14px;
            color: #666;
        }

        .nearby-attractions {
            padding: 16px;
        }

        .nearby-attractions h4 {
            font-size: 14px;
            margin-bottom: 12px;
            color: #333;
        }

        .attraction-list {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        .attraction-item {
            display: flex;
            align-items: center;
            padding: 12px;
            background: #f8f9fa;
            border-radius: 8px;
            cursor: pointer;
        }

        .attraction-icon {
            font-size: 20px;
            margin-right: 12px;
        }

        .attraction-info {
            flex: 1;
        }

        .attraction-info h5 {
            font-size: 14px;
            font-weight: 600;
            margin-bottom: 2px;
        }

        .attraction-info p {
            font-size: 12px;
            color: #666;
        }

        .route-btn {
            padding: 6px 12px;
            background: #2196f3;
            color: white;
            border: none;
            border-radius: 15px;
            font-size: 12px;
            cursor: pointer;
        }

        .voice-interaction {
            padding: 16px;
            border-top: 1px solid #e0e0e0;
            text-align: center;
        }

        .voice-interaction h4 {
            font-size: 14px;
            margin-bottom: 8px;
        }

        .voice-status {
            font-size: 12px;
            color: #666;
            margin-bottom: 12px;
        }

        .voice-control-btn {
            padding: 10px 20px;
            background: #4caf50;
            color: white;
            border: none;
            border-radius: 25px;
            font-size: 14px;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 8px;
            margin: 0 auto;
        }

        .quick-queries {
            padding: 16px;
            border-top: 1px solid #e0e0e0;
        }

        .quick-queries h4 {
            font-size: 14px;
            margin-bottom: 12px;
        }

        .query-buttons {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 8px;
        }

        .query-buttons button {
            padding: 8px 12px;
            background: white;
            border: 1px solid #e0e0e0;
            border-radius: 6px;
            font-size: 12px;
            cursor: pointer;
        }

        .route-recommend-panel {
            position: fixed;
            bottom: -100%;
            left: 0;
            width: 100%;
            max-width: 414px;
            background: white;
            border-radius: 16px 16px 0 0;
            box-shadow: 0 -4px 20px rgba(0,0,0,0.1);
            transition: bottom 0.3s ease;
            z-index: 1000;
        }

        .route-recommend-panel.active {
            bottom: 0;
        }

        /* 游记生成页面样式 */
        .travel-note-container {
            padding: 16px;
            max-height: calc(100vh - 140px);
            overflow-y: auto;
        }

        .note-form {
            background: white;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .image-upload-section h3,
        .text-input-section h3,
        .note-settings h3 {
            font-size: 16px;
            margin-bottom: 12px;
            color: #333;
        }

        .upload-area {
            border: 2px dashed #e0e0e0;
            border-radius: 8px;
            padding: 40px 20px;
            text-align: center;
            cursor: pointer;
            transition: border-color 0.3s;
            margin-bottom: 16px;
        }

        .upload-area:hover {
            border-color: #2196f3;
        }

        .upload-icon {
            font-size: 48px;
            margin-bottom: 8px;
        }

        .upload-hint {
            font-size: 12px;
            color: #888;
        }

        .image-preview {
            display: flex;
            gap: 8px;
            overflow-x: auto;
            padding: 8px 0;
        }

        .preview-image {
            min-width: 80px;
            height: 80px;
            border-radius: 8px;
            background-size: cover;
            background-position: center;
            position: relative;
        }

        .remove-image {
            position: absolute;
            top: -8px;
            right: -8px;
            width: 20px;
            height: 20px;
            background: #ff5722;
            color: white;
            border: none;
            border-radius: 50%;
            font-size: 12px;
            cursor: pointer;
        }

        #travelDescription {
            width: 100%;
            height: 120px;
            padding: 12px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            resize: none;
            font-size: 14px;
            line-height: 1.5;
        }

        .setting-group {
            margin-bottom: 16px;
        }

        .setting-group label {
            display: block;
            font-size: 14px;
            font-weight: 600;
            margin-bottom: 6px;
        }

        .setting-group input {
            width: 100%;
            padding: 10px;
            border: 1px solid #e0e0e0;
            border-radius: 6px;
            font-size: 14px;
        }

        .style-options {
            display: flex;
            gap: 8px;
            flex-wrap: wrap;
        }

        .style-btn {
            padding: 8px 16px;
            border: 1px solid #e0e0e0;
            background: white;
            border-radius: 20px;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.2s;
        }

        .style-btn.active {
            background: #2196f3;
            color: white;
            border-color: #2196f3;
        }

        .generate-note-btn {
            width: 100%;
            padding: 14px;
            background: linear-gradient(135deg, #2196f3, #4caf50);
            color: white;
            border: none;
            border-radius: 25px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: transform 0.2s;
        }

        .generate-note-btn:hover {
            transform: translateY(-2px);
        }

        .generated-note {
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            display: none;
        }

        .generated-note.active {
            display: block;
        }

        .note-header h2 {
            font-size: 20px;
            margin-bottom: 8px;
            color: #333;
        }

        .note-meta {
            display: flex;
            gap: 12px;
            font-size: 12px;
            color: #666;
            margin-bottom: 16px;
        }

        .note-opening {
            font-style: italic;
            color: #555;
            margin-bottom: 16px;
            padding: 12px;
            background: #f8f9fa;
            border-left: 4px solid #2196f3;
        }

        .note-images {
            margin: 16px 0;
        }

        .note-body {
            line-height: 1.6;
            color: #333;
        }

        .note-body p {
            margin-bottom: 12px;
        }

        .note-footer {
            margin-top: 20px;
            padding-top: 16px;
            border-top: 1px solid #e0e0e0;
            text-align: center;
        }

        .note-footer em {
            color: #666;
            font-size: 14px;
        }

        .note-actions {
            display: flex;
            gap: 8px;
            margin-top: 16px;
            padding-top: 16px;
            border-top: 1px solid #e0e0e0;
        }

        .note-actions button {
            flex: 1;
            padding: 10px;
            border: 1px solid #e0e0e0;
            border-radius: 6px;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.2s;
        }

        .note-actions button:first-child {
            background: #2196f3;
            color: white;
            border-color: #2196f3;
        }

        .note-actions button:nth-child(2) {
            background: #4caf50;
            color: white;
            border-color: #4caf50;
        }

        .note-actions button:last-child {
            background: #ff9800;
            color: white;
            border-color: #ff9800;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 首页内容 -->
        <div id="home" class="page-content active">
            <!-- 头部名称和介绍 -->
            <div class="home-header">
                <div class="header-bg"></div>
                <div class="header-content">
                    <h1 class="app-title">合小创</h1>
                    <span class="separator">·</span>
                    <p class="app-subtitle">打造 "科文旅" 融合标杆</p>
                </div>
            </div>

            <!-- Banner轮播 - 紧凑展示 -->
            <div class="banner compact">
                <div class="banner-slider" id="bannerSlider">
                    <div class="banner-item slide1">
                        <div class="banner-content">
                            <div class="banner-title">骆岗公园</div>
                            <div class="banner-subtitle">文化集中区 · 城市绿肺</div>
                        </div>
                    </div>
                    <div class="banner-item slide2">
                        <div class="banner-content">
                            <div class="banner-title">环巢湖片区</div>
                            <div class="banner-subtitle">生态休闲 · 湖光山色</div>
                        </div>
                    </div>
                    <div class="banner-item slide3">
                        <div class="banner-content">
                            <div class="banner-title">庐江山水</div>
                            <div class="banner-subtitle">康养度假 · 山水相依</div>
                        </div>
                    </div>
                </div>
                <div class="banner-dots">
                    <div class="dot active" onclick="goToSlide(0)"></div>
                    <div class="dot" onclick="goToSlide(1)"></div>
                    <div class="dot" onclick="goToSlide(2)"></div>
                </div>
            </div>
            
            <!-- 合肥旅游圈 - 紧凑设计 -->
            <div class="tourism-circle compact">
                <div class="section-title-small">🌟 合肥旅游圈</div>
                <div class="map-container-compact">
                    <div class="map-filters-compact">
                        <div class="filter-btn-small active" onclick="filterMap('all')">全部</div>
                        <div class="filter-btn-small" onclick="filterMap('scenic')">特色旅游</div>
                        <div class="filter-btn-small" onclick="filterMap('culture')">历史文化</div>
                        <div class="filter-btn-small" onclick="filterMap('family')">亲子科技</div>
                    </div>
                    <div class="map-display-compact">
                        <div class="map-spot-small spot1" onclick="goToAttractionDetail('骆岗公园')">
                            <div class="spot-image-small" style="background-image: url('https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=40&h=40&fit=crop')"></div>
                            <div class="spot-name-small">骆岗公园</div>
                        </div>
                        <div class="map-spot-small spot2" onclick="goToAttractionDetail('环巢湖')">
                            <div class="spot-image-small" style="background-image: url('https://images.unsplash.com/photo-1441974231531-c6227db76b6e?w=40&h=40&fit=crop')"></div>
                            <div class="spot-name-small">环巢湖</div>
                        </div>
                        <div class="map-spot-small spot3" onclick="goToAttractionDetail('三河古镇')">
                            <div class="spot-image-small" style="background-image: url('https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=40&h=40&fit=crop')"></div>
                            <div class="spot-name-small">三河古镇</div>
                        </div>
                        <div class="map-spot-small spot4" onclick="goToAttractionDetail('合肥科技馆')">
                            <div class="spot-image-small" style="background-image: url('https://images.unsplash.com/photo-1518709268805-4e9042af2176?w=40&h=40&fit=crop')"></div>
                            <div class="spot-name-small">科技馆</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 智能服务 - 渐变色背景 -->
            <div class="smart-services gradient">
                <div class="section-title-small">🤖 智能服务</div>
                <div class="services-grid-gradient">
                    <div class="service-item-gradient route-planning" onclick="switchPage('route-planning')">
                        <div class="service-icon-gradient">🗺️</div>
                        <div class="service-name-gradient">路径规划</div>
                    </div>
                    <div class="service-item-gradient guide-service" onclick="switchPage('guide-service')">
                        <div class="service-icon-gradient">🎧</div>
                        <div class="service-name-gradient">伴游导览</div>
                    </div>
                    <div class="service-item-gradient travel-note" onclick="switchPage('travel-note')">
                        <div class="service-icon-gradient">📝</div>
                        <div class="service-name-gradient">游记生成</div>
                    </div>
                </div>
            </div>

            <!-- 推荐活动 - 紧凑设计 -->
            <div class="activities compact">
                <div class="section-title-small">🎉 推荐活动</div>
                <div class="activities-scroll-compact">
                    <div class="activity-card-compact" onclick="showActivityDetail('春季花展')">
                        <div class="activity-image-compact" style="background-image: url('https://images.unsplash.com/photo-1490750967868-88aa4486c946?w=300')"></div>
                        <div class="activity-content-compact">
                            <div class="activity-title-compact">春季花展</div>
                            <div class="activity-time-compact">3月15日-4月30日</div>
                            <div class="activity-status-compact">进行中</div>
                        </div>
                    </div>
                    <div class="activity-card-compact" onclick="showActivityDetail('巢湖音乐节')">
                        <div class="activity-image-compact" style="background-image: url('https://images.unsplash.com/photo-1493225457124-a3eb161ffa5f?w=300')"></div>
                        <div class="activity-content-compact">
                            <div class="activity-title-compact">巢湖音乐节</div>
                            <div class="activity-time-compact">4月20日-4月22日</div>
                            <div class="activity-status-compact">即将开始</div>
                        </div>
                    </div>
                    <div class="activity-card-compact" onclick="showActivityDetail('科技体验周')">
                        <div class="activity-image-compact" style="background-image: url('https://images.unsplash.com/photo-1518709268805-4e9042af2176?w=300')"></div>
                        <div class="activity-content-compact">
                            <div class="activity-title-compact">科技体验周</div>
                            <div class="activity-time-compact">4月10日-4月16日</div>
                            <div class="activity-status-compact">进行中</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 游玩路线推荐 - 紧凑设计 -->
            <div class="route-recommendations compact">
                <div class="section-title-small">🚗 游玩路线推荐</div>
                <div class="routes-scroll">
                    <div class="route-card" onclick="selectRecommendedRoute('亲子游')">
                        <div class="route-image" style="background-image: url('https://images.unsplash.com/photo-1544551763-46a013bb70d5?w=300&h=150&fit=crop')"></div>
                        <div class="route-content">
                            <h3>两日亲子游</h3>
                            <p>动物园 → 科技馆 → 融创乐园</p>
                            <div class="route-meta">
                                <span>⏱️ 2天</span>
                                <span>👨‍👩‍👧‍👦 亲子</span>
                            </div>
                            <div class="route-price-tag">¥680/人</div>
                        </div>
                    </div>

                    <div class="route-card" onclick="selectRecommendedRoute('文化游')">
                        <div class="route-image" style="background-image: url('https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=300&h=150&fit=crop')"></div>
                        <div class="route-content">
                            <h3>三天文化游</h3>
                            <p>三河古镇 → 三国遗址 → 渡江纪念馆 → 包公园</p>
                            <div class="route-meta">
                                <span>⏱️ 3天</span>
                                <span>🏛️ 文化</span>
                            </div>
                            <div class="route-price-tag">¥520/人</div>
                        </div>
                    </div>

                    <div class="route-card" onclick="selectRecommendedRoute('生态游')">
                        <div class="route-image" style="background-image: url('https://images.unsplash.com/photo-1441974231531-c6227db76b6e?w=300&h=150&fit=crop')"></div>
                        <div class="route-content">
                            <h3>两天生态休闲</h3>
                            <p>环巢湖生态文化旅游区</p>
                            <div class="route-meta">
                                <span>⏱️ 2天</span>
                                <span>🌿 生态</span>
                            </div>
                            <div class="route-price-tag">¥450/人</div>
                        </div>
                    </div>

                    <div class="route-card" onclick="selectRecommendedRoute('骆岗游')">
                        <div class="route-image" style="background-image: url('https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=300&h=150&fit=crop')"></div>
                        <div class="route-content">
                            <h3>一天骆岗游</h3>
                            <p>骆岗公园深度体验</p>
                            <div class="route-meta">
                                <span>⏱️ 1天</span>
                                <span>🏞️ 公园</span>
                            </div>
                            <div class="route-price-tag">¥180/人</div>
                        </div>
                    </div>

                    <div class="route-card" onclick="selectRecommendedRoute('康养游')">
                        <div class="route-image" style="background-image: url('https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=300&h=150&fit=crop')"></div>
                        <div class="route-content">
                            <h3>两天康养度假</h3>
                            <p>庐江山水康养之旅</p>
                            <div class="route-meta">
                                <span>⏱️ 2天</span>
                                <span>🧘‍♀️ 康养</span>
                            </div>
                            <div class="route-price-tag">¥580/人</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 路径规划页面 -->
        <div id="route-planning" class="page-content">
            <div class="page-header">
                <button class="back-btn" onclick="switchPage('home')">←</button>
                <h1>路径规划</h1>
                <div class="header-actions"></div>
            </div>

            <!-- 悬浮按钮 - 位于banner下方，地图上方 -->
            <div class="floating-buttons-top">
                <button class="float-btn active" onclick="switchRouteMode('recommended', this)">推荐路线</button>
                <button class="float-btn" onclick="switchRouteMode('ai-planning', this)">AI规划</button>
            </div>

            <div class="route-planning-container">
                <!-- 地图背景 -->
                <div class="route-map-container">
                    <div class="route-map">
                        <!-- 合肥手绘地图背景 -->
                        <div class="map-background"></div>

                        <!-- 路线展示区域 -->
                        <div id="routeDisplay" class="route-display">
                            <!-- 路线点将在这里动态显示 -->
                        </div>
                    </div>
                </div>

                <!-- 推荐路线横向展示 -->
                <div id="recommended-routes" class="route-content active">
                    <div class="routes-horizontal-scroll">
                        <div class="route-card-small" onclick="selectRoute('亲子游')">
                            <div class="route-card-image" style="background-image: url('https://images.unsplash.com/photo-1544551763-46a013bb70d5?w=120&h=80&fit=crop')"></div>
                            <div class="route-card-info">
                                <h4>两日亲子游</h4>
                                <span class="route-card-price">¥680/人</span>
                            </div>
                        </div>

                        <div class="route-card-small" onclick="selectRoute('文化游')">
                            <div class="route-card-image" style="background-image: url('https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=120&h=80&fit=crop')"></div>
                            <div class="route-card-info">
                                <h4>三天文化游</h4>
                                <span class="route-card-price">¥520/人</span>
                            </div>
                        </div>

                        <div class="route-card-small" onclick="selectRoute('生态游')">
                            <div class="route-card-image" style="background-image: url('https://images.unsplash.com/photo-1441974231531-c6227db76b6e?w=120&h=80&fit=crop')"></div>
                            <div class="route-card-info">
                                <h4>两天生态游</h4>
                                <span class="route-card-price">¥450/人</span>
                            </div>
                        </div>

                        <div class="route-card-small" onclick="selectRoute('骆岗游')">
                            <div class="route-card-image" style="background-image: url('https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=120&h=80&fit=crop')"></div>
                            <div class="route-card-info">
                                <h4>一天骆岗游</h4>
                                <span class="route-card-price">¥180/人</span>
                            </div>
                        </div>

                        <div class="route-card-small" onclick="selectRoute('康养游')">
                            <div class="route-card-image" style="background-image: url('https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=120&h=80&fit=crop')"></div>
                            <div class="route-card-info">
                                <h4>两天康养游</h4>
                                <span class="route-card-price">¥580/人</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 选中路线的途经点展示 -->
                <div id="route-spots-display" class="route-content">
                    <div class="spots-horizontal-scroll">
                        <!-- 途经点将在这里动态显示 -->
                    </div>
                    <div class="route-actions-bottom">
                        <button onclick="goBackToRoutes()">← 返回路线选择</button>
                        <button onclick="saveCurrentRoute()">保存到行程</button>
                    </div>
                </div>

                <!-- AI规划内容 -->
                <div id="ai-planning-routes" class="route-content">
                    <div class="ai-planning-form">
                        <div class="form-group">
                            <label>游玩时间</label>
                            <div class="option-buttons">
                                <button class="option-btn active" onclick="selectOption('days', '1', this)">1天</button>
                                <button class="option-btn" onclick="selectOption('days', '2', this)">2天</button>
                                <button class="option-btn" onclick="selectOption('days', '3', this)">3天</button>
                                <button class="option-btn" onclick="selectOption('days', '4-7', this)">4-7天</button>
                            </div>
                        </div>

                        <div class="form-group">
                            <label>人数</label>
                            <div class="option-buttons">
                                <button class="option-btn active" onclick="selectOption('people', '1', this)">1人</button>
                                <button class="option-btn" onclick="selectOption('people', '2', this)">2人</button>
                                <button class="option-btn" onclick="selectOption('people', '3-5', this)">3-5人</button>
                                <button class="option-btn" onclick="selectOption('people', '6+', this)">6人以上</button>
                            </div>
                        </div>

                        <div class="form-group">
                            <label>旅游类型</label>
                            <div class="option-buttons">
                                <button class="option-btn active" onclick="selectOption('type', '文化历史', this)">文化历史</button>
                                <button class="option-btn" onclick="selectOption('type', '自然生态', this)">自然生态</button>
                                <button class="option-btn" onclick="selectOption('type', '亲子游乐', this)">亲子游乐</button>
                                <button class="option-btn" onclick="selectOption('type', '康养度假', this)">康养度假</button>
                                <button class="option-btn" onclick="selectOption('type', '美食体验', this)">美食体验</button>
                            </div>
                        </div>

                        <div class="form-group">
                            <label>预算范围</label>
                            <div class="option-buttons">
                                <button class="option-btn active" onclick="selectOption('budget', '500以下', this)">500元以下</button>
                                <button class="option-btn" onclick="selectOption('budget', '500-1000', this)">500-1000元</button>
                                <button class="option-btn" onclick="selectOption('budget', '1000-2000', this)">1000-2000元</button>
                                <button class="option-btn" onclick="selectOption('budget', '2000以上', this)">2000元以上</button>
                            </div>
                        </div>

                        <div class="form-group">
                            <label>其他需求</label>
                            <textarea id="otherRequirements" placeholder="请描述您的特殊需求..."></textarea>
                        </div>

                        <button class="generate-btn" onclick="generateAIRoute()">🚀 生成专属路线</button>
                    </div>
                </div>

                <!-- AI生成的路线展示 -->
                <div id="ai-generated-route" class="route-content">
                    <div class="generated-spots-scroll">
                        <!-- AI生成的途经点将在这里显示 -->
                    </div>
                    <div class="route-actions-bottom">
                        <button onclick="goBackToAIPlanning()">← 重新规划</button>
                        <button onclick="saveAIRoute()">保存到行程</button>
                    </div>
                </div>
            </div>

            <!-- 路线详情底部面板 -->
            <div id="routeDetailPanel" class="route-detail-panel">
                <div class="panel-header">
                    <h3 id="routeTitle">路线详情</h3>
                    <button onclick="closeRouteDetail()">×</button>
                </div>
                <div class="panel-content">
                    <div class="route-summary">
                        <div id="routeSummary"></div>
                    </div>
                    <div class="route-spots-scroll">
                        <div id="routeSpots" class="route-spots"></div>
                    </div>
                    <div class="route-actions">
                        <button class="edit-route-btn" onclick="editRoute()">编辑路线</button>
                        <button class="save-route-btn" onclick="saveRoute()">保存行程</button>
                        <button class="pay-route-btn" onclick="payRoute()">一键付费</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 探索页面 -->
        <div id="explore" class="page-content">
            <div class="header">
                <h1>探索合肥</h1>
            </div>

            <div style="padding: 20px 16px;">
                <div class="explore-tabs">
                    <div class="tab-btn active" onclick="switchExploreTab('attractions', this)">景点</div>
                    <div class="tab-btn" onclick="switchExploreTab('food', this)">美食</div>
                    <div class="tab-btn" onclick="switchExploreTab('hotels', this)">酒店</div>
                    <div class="tab-btn" onclick="switchExploreTab('guides', this)">攻略</div>
                    <div class="tab-btn" onclick="switchExploreTab('notes', this)">游记</div>
                </div>

                <!-- 景点列表 -->
                <div id="attractions-content" class="explore-content active">
                    <div class="filter-section">
                        <select class="filter-select" onchange="filterAttractions(this.value)">
                            <option value="all">全部类型</option>
                            <option value="park">公园</option>
                            <option value="culture">文化</option>
                            <option value="tech">科技</option>
                        </select>
                    </div>

                    <div class="attraction-list">
                        <div class="attraction-item" onclick="showAttractionDetail('骆岗公园')">
                            <div class="attraction-image" style="background-image: url('https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=300')"></div>
                            <div class="attraction-info">
                                <h3>骆岗公园</h3>
                                <p>合肥最大的城市公园，集文化、生态于一体</p>
                                <div class="attraction-meta">
                                    <span>📍 2.5km</span>
                                    <span>💰 免费</span>
                                    <span>⭐ 4.8</span>
                                </div>
                            </div>
                        </div>

                        <div class="attraction-item" onclick="showAttractionDetail('三河古镇')">
                            <div class="attraction-image" style="background-image: url('https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=300')"></div>
                            <div class="attraction-info">
                                <h3>三河古镇</h3>
                                <p>千年古镇，江南水乡风情</p>
                                <div class="attraction-meta">
                                    <span>📍 45km</span>
                                    <span>💰 ¥80</span>
                                    <span>⭐ 4.6</span>
                                </div>
                            </div>
                        </div>

                        <div class="attraction-item" onclick="showAttractionDetail('合肥科技馆')">
                            <div class="attraction-image" style="background-image: url('https://images.unsplash.com/photo-1518709268805-4e9042af2176?w=300')"></div>
                            <div class="attraction-info">
                                <h3>合肥科技馆</h3>
                                <p>现代科技体验，适合亲子游玩</p>
                                <div class="attraction-meta">
                                    <span>📍 8km</span>
                                    <span>💰 ¥60</span>
                                    <span>⭐ 4.7</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 美食列表 -->
                <div id="food-content" class="explore-content">
                    <div class="food-list">
                        <div class="food-item">
                            <div class="food-image" style="background-image: url('https://images.unsplash.com/photo-1565299624946-b28f40a0ca4b?w=300')"></div>
                            <div class="food-info">
                                <h3>庐州烤鸭</h3>
                                <div class="food-meta">
                                    <span>🍽️ 徽菜</span>
                                    <span>📍 1.2km</span>
                                    <span>💰 ¥85/人</span>
                                </div>
                                <div class="food-tags">
                                    <span class="tag">招牌菜</span>
                                    <span class="tag">环境优雅</span>
                                </div>
                            </div>
                        </div>

                        <div class="food-item">
                            <div class="food-image" style="background-image: url('https://images.unsplash.com/photo-1504674900247-0877df9cc836?w=300')"></div>
                            <div class="food-info">
                                <h3>包河藕圆</h3>
                                <div class="food-meta">
                                    <span>🍽️ 小吃</span>
                                    <span>📍 0.8km</span>
                                    <span>💰 ¥25/人</span>
                                </div>
                                <div class="food-tags">
                                    <span class="tag">特色小吃</span>
                                    <span class="tag">老字号</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 伴游导览页面 -->
        <div id="guide-service" class="page-content">
            <div class="page-header">
                <button class="back-btn" onclick="switchPage('home')">←</button>
                <h1>伴游导览</h1>
                <div class="header-actions">
                    <button class="voice-btn" onclick="toggleVoiceGuide()">🎤</button>
                </div>
            </div>

            <div class="guide-container">
                <!-- 地图区域 -->
                <div class="guide-map-container">
                    <div class="guide-map">
                        <!-- 合肥手绘地图背景 -->
                        <div class="map-background"></div>

                        <!-- 当前位置 -->
                        <div class="current-location">
                            <div class="location-marker">📍</div>
                            <div class="location-text">您的位置</div>
                        </div>

                        <!-- 附近景点 -->
                        <div class="nearby-spot spot-luogang" onclick="playSpotAudio('骆岗公园')">
                            <div class="spot-marker blinking">🏞️</div>
                            <div class="spot-label">骆岗公园</div>
                        </div>

                        <div class="nearby-spot spot-tech" onclick="playSpotAudio('科技馆')">
                            <div class="spot-marker">🔬</div>
                            <div class="spot-label">科技馆</div>
                        </div>

                        <div class="nearby-spot spot-chaohu" onclick="playSpotAudio('环巢湖')">
                            <div class="spot-marker">🌊</div>
                            <div class="spot-label">环巢湖</div>
                        </div>
                    </div>
                </div>

                <!-- 功能面板 -->
                <div class="guide-panel">
                    <!-- 当前位置 - 弱化显示 -->
                    <div class="location-info-compact">
                        <span>📍 政务区</span>
                    </div>

                    <!-- 附近景点 - 横向紧凑展示 -->
                    <div class="nearby-attractions-compact">
                        <h4>🎯 附近景点</h4>
                        <div class="attractions-horizontal">
                            <div class="attraction-compact" onclick="highlightSpot('骆岗公园')">
                                <div class="attraction-icon-small">🏞️</div>
                                <div class="attraction-name">骆岗公园</div>
                                <div class="attraction-distance">2.5km</div>
                            </div>

                            <div class="attraction-compact" onclick="highlightSpot('合肥科技馆')">
                                <div class="attraction-icon-small">🔬</div>
                                <div class="attraction-name">科技馆</div>
                                <div class="attraction-distance">8km</div>
                            </div>

                            <div class="attraction-compact" onclick="highlightSpot('环巢湖')">
                                <div class="attraction-icon-small">🌊</div>
                                <div class="attraction-name">环巢湖</div>
                                <div class="attraction-distance">35km</div>
                            </div>

                            <div class="attraction-compact" onclick="highlightSpot('三河古镇')">
                                <div class="attraction-icon-small">🏛️</div>
                                <div class="attraction-name">三河古镇</div>
                                <div class="attraction-distance">45km</div>
                            </div>
                        </div>
                    </div>

                    <!-- 智能搜索区域 -->
                    <div class="smart-search">
                        <!-- 快速查询悬浮按钮 -->
                        <div class="quick-tags">
                            <button class="quick-tag" onclick="quickSearch('餐厅')">附近餐厅</button>
                            <button class="quick-tag" onclick="quickSearch('停车场')">停车场</button>
                            <button class="quick-tag" onclick="quickSearch('地铁口')">地铁口</button>
                        </div>

                        <!-- 输入框 -->
                        <div class="search-input-container">
                            <input type="text" id="searchInput" placeholder="搜索附近的地点或设施..." class="search-input">
                            <button class="input-mode-btn" onclick="toggleInputMode()" id="inputModeBtn">🎤</button>
                            <button class="search-btn" onclick="performSearch()">搜索</button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 路线推荐面板 -->
            <div id="routeRecommendPanel" class="route-recommend-panel">
                <div class="panel-header">
                    <h3>🚗 路线推荐</h3>
                    <button onclick="closeRouteRecommend()">×</button>
                </div>
                <div class="panel-content">
                    <div id="routeOptions" class="route-options">
                        <!-- 路线选项将在这里动态显示 -->
                    </div>
                </div>
            </div>
        </div>

        <!-- 游记生成页面 -->
        <div id="travel-note" class="page-content">
            <div class="page-header">
                <button class="back-btn" onclick="switchPage('home')">←</button>
                <h1>游记生成</h1>
                <div class="header-actions">
                    <button class="preview-btn" onclick="previewNote()">预览</button>
                </div>
            </div>

            <div class="travel-note-container">
                <div class="note-form">
                    <!-- 图片上传区域 -->
                    <div class="image-upload-section">
                        <h3>📸 上传照片</h3>
                        <div class="upload-area" onclick="triggerImageUpload()">
                            <div class="upload-placeholder">
                                <div class="upload-icon">📷</div>
                                <p>点击上传照片</p>
                                <p class="upload-hint">支持多张图片上传</p>
                            </div>
                            <input type="file" id="imageUpload" multiple accept="image/*" style="display: none;" onchange="handleImageUpload(event)">
                        </div>

                        <!-- 已上传图片预览 -->
                        <div id="imagePreview" class="image-preview">
                            <!-- 图片预览将在这里显示 -->
                        </div>
                    </div>

                    <!-- 文字描述区域 -->
                    <div class="text-input-section">
                        <h3>✍️ 文字描述</h3>
                        <textarea id="travelDescription" placeholder="请描述您的旅行体验、感受、见闻等...&#10;例如：今天游览了骆岗公园，天气很好，公园里绿树成荫，湖水清澈..."></textarea>
                    </div>

                    <!-- 游记设置 -->
                    <div class="note-settings">
                        <h3>⚙️ 游记设置</h3>
                        <div class="setting-group">
                            <label>游记标题</label>
                            <input type="text" id="noteTitle" placeholder="请输入游记标题">
                        </div>

                        <div class="setting-group">
                            <label>游记风格</label>
                            <div class="style-options">
                                <button class="style-btn active" onclick="selectStyle('文艺', this)">文艺风</button>
                                <button class="style-btn" onclick="selectStyle('实用', this)">实用攻略</button>
                                <button class="style-btn" onclick="selectStyle('趣味', this)">趣味日记</button>
                                <button class="style-btn" onclick="selectStyle('简约', this)">简约风</button>
                            </div>
                        </div>

                        <div class="setting-group">
                            <label>标签</label>
                            <div class="tag-input">
                                <input type="text" id="noteTags" placeholder="添加标签，用逗号分隔">
                            </div>
                        </div>
                    </div>

                    <!-- 生成按钮 -->
                    <div class="generate-section">
                        <button class="generate-note-btn" onclick="generateTravelNote()">
                            ✨ AI生成游记
                        </button>
                    </div>
                </div>

                <!-- 生成的游记预览 -->
                <div id="generatedNote" class="generated-note">
                    <!-- 生成的游记内容将在这里显示 -->
                </div>
            </div>
        </div>

        <!-- 伴游助手页面 -->
        <div id="assistant" class="page-content">
            <div class="header">
                <h1>伴游助手</h1>
            </div>

            <div style="padding: 20px 16px;">
                <div class="chat-container">
                    <div class="chat-messages" id="chatMessages">
                        <div class="message bot-message">
                            <div class="message-content">
                                您好！我是您的专属伴游助手，可以为您提供合肥旅游相关的任何帮助。您可以问我关于景点、美食、交通等问题。
                            </div>
                        </div>
                    </div>

                    <div class="quick-questions">
                        <div class="section-title">💡 常见问题</div>
                        <div class="question-tags">
                            <div class="question-tag" onclick="askQuestion('骆岗公园怎么去？')">骆岗公园怎么去？</div>
                            <div class="question-tag" onclick="askQuestion('合肥有什么特色美食？')">合肥有什么特色美食？</div>
                            <div class="question-tag" onclick="askQuestion('三河古镇门票多少钱？')">三河古镇门票多少钱？</div>
                            <div class="question-tag" onclick="askQuestion('适合亲子游的景点推荐')">适合亲子游的景点推荐</div>
                        </div>
                    </div>

                    <div class="chat-input-container">
                        <input type="text" id="chatInput" placeholder="请输入您的问题..." class="chat-input">
                        <button onclick="sendMessage()" class="send-btn">发送</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 行程页面 -->
        <div id="itinerary" class="page-content">
            <div class="header">
                <h1>我的行程</h1>
            </div>

            <div style="padding: 20px 16px;">
                <div class="itinerary-tabs">
                    <div class="tab-btn active" onclick="switchItineraryTab('planning', this)">行程规划</div>
                    <div class="tab-btn" onclick="switchItineraryTab('my-trips', this)">我的行程</div>
                </div>

                <div id="planning-content" class="itinerary-content active">
                    <div class="planning-card">
                        <h3>🗺️ 智能路径规划</h3>
                        <p>基于AI为您定制专属旅游路线</p>
                        <button class="primary-btn" onclick="openRouteService()">开始规划</button>
                    </div>
                </div>

                <div id="my-trips-content" class="itinerary-content">
                    <div class="trip-list">
                        <div class="trip-item">
                            <div class="trip-header">
                                <h3>合肥三日文化游</h3>
                                <span class="trip-status">进行中</span>
                            </div>
                            <div class="trip-info">
                                <p>📅 2024年4月15日 - 4月17日</p>
                                <p>👥 2人 · 💰 ¥1,200</p>
                            </div>
                            <div class="trip-actions">
                                <button onclick="viewTripDetail('trip1')">查看详情</button>
                                <button onclick="editTrip('trip1')">编辑</button>
                            </div>
                        </div>

                        <div class="trip-item">
                            <div class="trip-header">
                                <h3>环巢湖生态游</h3>
                                <span class="trip-status completed">已完成</span>
                            </div>
                            <div class="trip-info">
                                <p>📅 2024年3月20日 - 3月21日</p>
                                <p>👥 4人 · 💰 ¥800</p>
                            </div>
                            <div class="trip-actions">
                                <button onclick="viewTripDetail('trip2')">查看详情</button>
                                <button onclick="deleteTrip('trip2')">删除</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 我的页面 -->
        <div id="profile" class="page-content">
            <div class="header">
                <h1>个人中心</h1>
            </div>

            <div style="padding: 20px 16px;">
                <div class="profile-card">
                    <div class="profile-avatar">
                        <img src="https://images.unsplash.com/photo-1535713875002-d1d0cf377fde?w=100" alt="头像">
                    </div>
                    <div class="profile-info">
                        <h3>旅行达人</h3>
                        <p>合肥文旅爱好者</p>
                    </div>
                </div>

                <div class="stats-grid">
                    <div class="stat-item">
                        <div class="stat-number">12</div>
                        <div class="stat-label">游记数</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">2,580</div>
                        <div class="stat-label">积分</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">8</div>
                        <div class="stat-label">足迹</div>
                    </div>
                </div>

                <div class="menu-list">
                    <div class="menu-item" onclick="showMyNotes()">
                        <span>📝 我的游记</span>
                        <span>></span>
                    </div>
                    <div class="menu-item" onclick="showMyOrders()">
                        <span>🎫 我的订单</span>
                        <span>></span>
                    </div>
                    <div class="menu-item" onclick="showSettings()">
                        <span>⚙️ 设置</span>
                        <span>></span>
                    </div>
                </div>
            </div>
        </div>

        <!-- 底部导航 -->
        <div class="bottom-nav">
            <div class="nav-item active" onclick="switchPage('home', this)">
                <div class="nav-icon">🏠</div>
                <div class="nav-text">首页</div>
            </div>
            <div class="nav-item" onclick="switchPage('explore', this)">
                <div class="nav-icon">🔍</div>
                <div class="nav-text">探索</div>
            </div>
            <div class="nav-item" onclick="switchPage('assistant', this)">
                <div class="nav-icon">🤖</div>
                <div class="nav-text">伴游助手</div>
            </div>
            <div class="nav-item" onclick="switchPage('itinerary', this)">
                <div class="nav-icon">📅</div>
                <div class="nav-text">行程</div>
            </div>
            <div class="nav-item" onclick="switchPage('profile', this)">
                <div class="nav-icon">👤</div>
                <div class="nav-text">我的</div>
            </div>
        </div>
    </div>

    <!-- 模态框 -->
    <div id="modal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <div class="modal-title" id="modalTitle">详情</div>
                <button class="close-btn" onclick="closeModal()">&times;</button>
            </div>
            <div id="modalBody">
                <!-- 模态框内容将在这里动态加载 -->
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        let currentSlide = 0;
        let currentPage = 'home';
        let currentExploreTab = 'attractions';
        let currentItineraryTab = 'planning';
        let currentRouteMode = 'recommended';
        let selectedStyle = '文艺';
        let uploadedImages = [];
        let selectedOptions = {
            days: '1',
            people: '1',
            type: '文化历史',
            budget: '500以下'
        };
        let isVoiceMode = false;

        // Banner轮播功能
        function goToSlide(index) {
            currentSlide = index;
            const slider = document.getElementById('bannerSlider');
            slider.style.transform = `translateX(-${index * 100}%)`;

            // 更新指示器
            document.querySelectorAll('.dot').forEach((dot, i) => {
                dot.classList.toggle('active', i === index);
            });
        }

        // 自动轮播
        setInterval(() => {
            currentSlide = (currentSlide + 1) % 3;
            goToSlide(currentSlide);
        }, 5000);

        // 页面切换功能
        function switchPage(page, element) {
            // 隐藏所有页面
            document.querySelectorAll('.page-content').forEach(p => {
                p.classList.remove('active');
            });

            // 显示目标页面
            document.getElementById(page).classList.add('active');

            // 更新导航状态
            document.querySelectorAll('.nav-item').forEach(item => {
                item.classList.remove('active');
            });

            // 如果有element参数，使用它；否则通过page查找对应的导航项
            if (element) {
                element.classList.add('active');
            } else {
                // 根据page找到对应的导航项
                const navItems = document.querySelectorAll('.nav-item');
                const pageIndex = ['home', 'explore', 'assistant', 'itinerary', 'profile'].indexOf(page);
                if (pageIndex >= 0 && navItems[pageIndex]) {
                    navItems[pageIndex].classList.add('active');
                }
            }

            currentPage = page;
        }

        // 智能服务功能
        function openRouteService() {
            showModal('路径规划', `
                <div style="text-align: center;">
                    <h3>🗺️ AI智能路径规划</h3>
                    <p>为您量身定制专属旅游路线</p>

                    <div style="margin: 20px 0;">
                        <h4>📍 推荐路线</h4>
                        <div style="display: flex; flex-direction: column; gap: 10px; margin: 10px 0;">
                            <button onclick="selectRoute('亲子游')" style="padding: 10px; border: 1px solid #e0e0e0; border-radius: 8px; background: white;">
                                👨‍👩‍👧‍👦 两日亲子游 - ¥680/人
                            </button>
                            <button onclick="selectRoute('文化游')" style="padding: 10px; border: 1px solid #e0e0e0; border-radius: 8px; background: white;">
                                🏛️ 三天文化游 - ¥520/人
                            </button>
                            <button onclick="selectRoute('生态游')" style="padding: 10px; border: 1px solid #e0e0e0; border-radius: 8px; background: white;">
                                🌿 两天生态休闲游 - ¥450/人
                            </button>
                            <button onclick="selectRoute('骆岗游')" style="padding: 10px; border: 1px solid #e0e0e0; border-radius: 8px; background: white;">
                                🏞️ 一天骆岗游 - ¥180/人
                            </button>
                            <button onclick="selectRoute('康养游')" style="padding: 10px; border: 1px solid #e0e0e0; border-radius: 8px; background: white;">
                                🧘‍♀️ 两天康养度假 - ¥580/人
                            </button>
                        </div>
                    </div>

                    <div style="margin: 20px 0;">
                        <h4>🤖 AI定制路线</h4>
                        <button onclick="openAIPlanning()" style="padding: 12px 24px; background: linear-gradient(135deg, #2196f3, #4caf50); color: white; border: none; border-radius: 25px;">
                            开始AI规划
                        </button>
                    </div>
                </div>
            `);
        }

        function openGuideService() {
            showModal('伴游导览', `
                <div style="text-align: center;">
                    <h3>🎧 智能伴游导览</h3>
                    <p>基于您的位置提供个性化导览服务</p>

                    <div style="margin: 20px 0;">
                        <div style="background: #f5f5f5; padding: 15px; border-radius: 8px; margin: 10px 0;">
                            📍 当前位置：合肥市政务区
                        </div>

                        <h4>附近景点</h4>
                        <div style="display: flex; flex-direction: column; gap: 8px; margin: 10px 0;">
                            <div onclick="playAudio('骆岗公园')" style="padding: 10px; border: 1px solid #e0e0e0; border-radius: 8px; background: white; cursor: pointer;">
                                🏞️ 骆岗公园 (2.5km) - 点击播放介绍
                            </div>
                            <div onclick="playAudio('合肥科技馆')" style="padding: 10px; border: 1px solid #e0e0e0; border-radius: 8px; background: white; cursor: pointer;">
                                🔬 合肥科技馆 (8km) - 点击播放介绍
                            </div>
                        </div>

                        <button onclick="startVoiceGuide()" style="padding: 12px 24px; background: #4caf50; color: white; border: none; border-radius: 25px; margin-top: 10px;">
                            🎤 语音交互
                        </button>
                    </div>
                </div>
            `);
        }

        function openTravelNoteService() {
            showModal('游记生成', `
                <div style="text-align: center;">
                    <h3>📝 AI游记生成</h3>
                    <p>基于您的旅行足迹自动生成精美游记</p>

                    <div style="margin: 20px 0;">
                        <div style="background: #f5f5f5; padding: 15px; border-radius: 8px; margin: 10px 0;">
                            <h4>选择行程</h4>
                            <select style="width: 100%; padding: 8px; border: 1px solid #e0e0e0; border-radius: 4px;">
                                <option>合肥三日文化游</option>
                                <option>环巢湖生态游</option>
                                <option>骆岗公园一日游</option>
                            </select>
                        </div>

                        <div style="background: #f5f5f5; padding: 15px; border-radius: 8px; margin: 10px 0;">
                            <h4>游记风格</h4>
                            <div style="display: flex; gap: 8px; justify-content: center;">
                                <button onclick="selectStyle('文艺')" style="padding: 6px 12px; border: 1px solid #e0e0e0; border-radius: 15px; background: white;">文艺风</button>
                                <button onclick="selectStyle('实用')" style="padding: 6px 12px; border: 1px solid #e0e0e0; border-radius: 15px; background: white;">实用攻略</button>
                                <button onclick="selectStyle('趣味')" style="padding: 6px 12px; border: 1px solid #e0e0e0; border-radius: 15px; background: white;">趣味日记</button>
                            </div>
                        </div>

                        <button onclick="generateTravelNote()" style="padding: 12px 24px; background: linear-gradient(135deg, #2196f3, #4caf50); color: white; border: none; border-radius: 25px;">
                            ✨ 生成游记
                        </button>
                    </div>
                </div>
            `);
        }

        // 地图功能
        function filterMap(type) {
            // 更新过滤按钮状态
            document.querySelectorAll('.filter-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            event.target.classList.add('active');

            // 这里可以添加地图点的显示/隐藏逻辑
            console.log('过滤地图类型:', type);
        }

        function showSpotInfo(spotName) {
            const spotInfo = {
                '骆岗公园': {
                    title: '骆岗公园',
                    description: '合肥最大的城市公园，集文化、生态、休闲于一体',
                    image: 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=400',
                    features: ['免费开放', '文化展览', '生态景观', '休闲娱乐'],
                    openTime: '全天开放',
                    address: '合肥市包河区骆岗街道'
                },
                '环巢湖': {
                    title: '环巢湖生态文化旅游区',
                    description: '中国五大淡水湖之一，生态休闲的绝佳去处',
                    image: 'https://images.unsplash.com/photo-1441974231531-c6227db76b6e?w=400',
                    features: ['湖光山色', '生态观光', '水上运动', '特色美食'],
                    openTime: '全天开放',
                    address: '合肥市环巢湖地区'
                },
                '三河古镇': {
                    title: '三河古镇',
                    description: '千年古镇，江南水乡风情浓郁',
                    image: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=400',
                    features: ['古建筑群', '水乡风情', '特色小吃', '历史文化'],
                    openTime: '8:00-17:30',
                    address: '合肥市肥西县三河镇'
                },
                '科技馆': {
                    title: '合肥科技馆',
                    description: '现代科技体验中心，适合亲子游玩',
                    image: 'https://images.unsplash.com/photo-1518709268805-4e9042af2176?w=400',
                    features: ['科技体验', '互动展览', '教育娱乐', '亲子活动'],
                    openTime: '9:00-17:00',
                    address: '合肥市蜀山区黄山路'
                }
            };

            const info = spotInfo[spotName];
            if (info) {
                showModal(info.title, `
                    <div>
                        <img src="${info.image}" style="width: 100%; height: 200px; object-fit: cover; border-radius: 8px; margin-bottom: 16px;">
                        <p style="color: #666; margin-bottom: 16px;">${info.description}</p>

                        <div style="margin-bottom: 16px;">
                            <h4>🌟 特色亮点</h4>
                            <div style="display: flex; flex-wrap: wrap; gap: 6px; margin-top: 8px;">
                                ${info.features.map(feature => `<span style="padding: 4px 8px; background: #e3f2fd; color: #2196f3; border-radius: 10px; font-size: 12px;">${feature}</span>`).join('')}
                            </div>
                        </div>

                        <div style="margin-bottom: 16px;">
                            <p><strong>🕒 开放时间：</strong>${info.openTime}</p>
                            <p><strong>📍 地址：</strong>${info.address}</p>
                        </div>

                        <div style="display: flex; gap: 8px; justify-content: center;">
                            <button onclick="addToItinerary('${spotName}')" style="padding: 8px 16px; background: #4caf50; color: white; border: none; border-radius: 20px;">加入行程</button>
                            <button onclick="showRoute('${spotName}')" style="padding: 8px 16px; background: #2196f3; color: white; border: none; border-radius: 20px;">查看路线</button>
                        </div>
                    </div>
                `);
            }
        }

        // 活动详情
        function showActivityDetail(activityName) {
            const activities = {
                '春季花展': {
                    title: '春季花展',
                    time: '2024年3月15日 - 4月30日',
                    location: '骆岗公园',
                    description: '春暖花开，百花齐放。骆岗公园春季花展邀您共赏春日美景。',
                    highlights: ['樱花大道', '郁金香花海', '摄影比赛', '花艺体验'],
                    price: '免费参观'
                },
                '巢湖音乐节': {
                    title: '巢湖音乐节',
                    time: '2024年4月20日 - 4月22日',
                    location: '环巢湖旅游区',
                    description: '湖光山色中的音乐盛宴，多位知名歌手倾情演出。',
                    highlights: ['明星演出', '湖边音乐', '美食集市', '露营体验'],
                    price: '¥180-680'
                },
                '科技体验周': {
                    title: '科技体验周',
                    time: '2024年4月10日 - 4月16日',
                    location: '合肥科技馆',
                    description: '最新科技成果展示，VR、AI等前沿技术体验。',
                    highlights: ['VR体验', 'AI互动', '机器人表演', '科普讲座'],
                    price: '¥30（学生半价）'
                }
            };

            const activity = activities[activityName];
            if (activity) {
                showModal(activity.title, `
                    <div>
                        <div style="background: linear-gradient(135deg, #2196f3, #4caf50); color: white; padding: 16px; border-radius: 8px; margin-bottom: 16px; text-align: center;">
                            <h3>${activity.title}</h3>
                            <p>📅 ${activity.time}</p>
                            <p>📍 ${activity.location}</p>
                        </div>

                        <p style="color: #666; margin-bottom: 16px;">${activity.description}</p>

                        <div style="margin-bottom: 16px;">
                            <h4>🌟 活动亮点</h4>
                            <div style="display: flex; flex-wrap: wrap; gap: 6px; margin-top: 8px;">
                                ${activity.highlights.map(highlight => `<span style="padding: 4px 8px; background: #e3f2fd; color: #2196f3; border-radius: 10px; font-size: 12px;">${highlight}</span>`).join('')}
                            </div>
                        </div>

                        <div style="margin-bottom: 16px;">
                            <p><strong>💰 价格：</strong>${activity.price}</p>
                        </div>

                        <div style="text-align: center;">
                            <button onclick="bookActivity('${activityName}')" style="padding: 12px 24px; background: linear-gradient(135deg, #2196f3, #4caf50); color: white; border: none; border-radius: 25px;">
                                立即预订
                            </button>
                        </div>
                    </div>
                `);
            }
        }

        // 探索页面功能
        function switchExploreTab(tab, element) {
            // 更新标签状态
            document.querySelectorAll('.explore-tabs .tab-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            if (element) {
                element.classList.add('active');
            }

            // 切换内容
            document.querySelectorAll('.explore-content').forEach(content => {
                content.classList.remove('active');
            });
            document.getElementById(tab + '-content').classList.add('active');

            currentExploreTab = tab;
        }

        function filterAttractions(type) {
            console.log('筛选景点类型:', type);
            // 这里可以添加实际的筛选逻辑
        }

        function showAttractionDetail(name) {
            showSpotInfo(name); // 复用景点信息显示功能
        }

        // 聊天功能
        function askQuestion(question) {
            const chatMessages = document.getElementById('chatMessages');

            // 添加用户消息
            const userMessage = document.createElement('div');
            userMessage.className = 'message user-message';
            userMessage.innerHTML = `<div class="message-content">${question}</div>`;
            chatMessages.appendChild(userMessage);

            // 模拟AI回复
            setTimeout(() => {
                const botMessage = document.createElement('div');
                botMessage.className = 'message bot-message';

                const responses = {
                    '骆岗公园怎么去？': '骆岗公园位于包河区，您可以乘坐地铁1号线到骆岗站，或者乘坐公交126路、150路等。从市中心打车约20分钟，费用大概25-35元。',
                    '合肥有什么特色美食？': '合肥特色美食有：庐州烤鸭、包河藕圆、三河米饺、吴山贡鹅、合肥龙虾等。推荐您去淮河路步行街或者罍街品尝地道美食。',
                    '三河古镇门票多少钱？': '三河古镇门票80元/人，学生票40元。包含古镇内主要景点：杨振宁旧居、刘同兴隆庄、鹤庐等。建议游玩时间3-4小时。',
                    '适合亲子游的景点推荐': '推荐亲子游景点：1.合肥科技馆（科普教育）2.合肥野生动物园（动物观赏）3.融创乐园（游乐设施）4.骆岗公园（户外活动）5.安徽博物院（历史文化）'
                };

                botMessage.innerHTML = `<div class="message-content">${responses[question] || '感谢您的提问，我会为您查找相关信息。如需更详细的帮助，请描述您的具体需求。'}</div>`;
                chatMessages.appendChild(botMessage);

                // 滚动到底部
                chatMessages.scrollTop = chatMessages.scrollHeight;
            }, 1000);

            // 滚动到底部
            chatMessages.scrollTop = chatMessages.scrollHeight;
        }

        function sendMessage() {
            const input = document.getElementById('chatInput');
            const message = input.value.trim();

            if (message) {
                askQuestion(message);
                input.value = '';
            }
        }

        // 回车发送消息
        document.addEventListener('DOMContentLoaded', function() {
            const chatInput = document.getElementById('chatInput');
            if (chatInput) {
                chatInput.addEventListener('keypress', function(e) {
                    if (e.key === 'Enter') {
                        sendMessage();
                    }
                });
            }
        });

        // 行程页面功能
        function switchItineraryTab(tab, element) {
            // 更新标签状态
            document.querySelectorAll('.itinerary-tabs .tab-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            if (element) {
                element.classList.add('active');
            }

            // 切换内容
            document.querySelectorAll('.itinerary-content').forEach(content => {
                content.classList.remove('active');
            });
            document.getElementById(tab + '-content').classList.add('active');

            currentItineraryTab = tab;
        }

        function viewTripDetail(tripId) {
            const trips = {
                'trip1': {
                    title: '合肥三日文化游',
                    dates: '2024年4月15日 - 4月17日',
                    people: '2人',
                    budget: '¥1,200',
                    status: '进行中',
                    itinerary: [
                        { day: 1, location: '三河古镇', time: '9:00-17:00', description: '游览古镇，品尝特色小吃' },
                        { day: 2, location: '包公园', time: '9:00-12:00', description: '了解包公文化' },
                        { day: 2, location: '安徽博物院', time: '14:00-17:00', description: '参观历史文物' },
                        { day: 3, location: '渡江战役纪念馆', time: '9:00-16:00', description: '学习革命历史' }
                    ]
                },
                'trip2': {
                    title: '环巢湖生态游',
                    dates: '2024年3月20日 - 3月21日',
                    people: '4人',
                    budget: '¥800',
                    status: '已完成',
                    itinerary: [
                        { day: 1, location: '巢湖湿地公园', time: '9:00-17:00', description: '观鸟赏景，生态体验' },
                        { day: 2, location: '姥山岛', time: '9:00-16:00', description: '乘船游湖，登岛观光' }
                    ]
                }
            };

            const trip = trips[tripId];
            if (trip) {
                showModal(trip.title, `
                    <div>
                        <div style="background: #f8f9fa; padding: 16px; border-radius: 8px; margin-bottom: 16px;">
                            <p><strong>📅 时间：</strong>${trip.dates}</p>
                            <p><strong>👥 人数：</strong>${trip.people}</p>
                            <p><strong>💰 预算：</strong>${trip.budget}</p>
                            <p><strong>📊 状态：</strong><span style="color: ${trip.status === '进行中' ? '#4caf50' : '#9e9e9e'}">${trip.status}</span></p>
                        </div>

                        <h4>📋 行程安排</h4>
                        <div style="margin-top: 12px;">
                            ${trip.itinerary.map(item => `
                                <div style="border-left: 3px solid #2196f3; padding-left: 12px; margin-bottom: 12px;">
                                    <div style="font-weight: 600;">第${item.day}天 - ${item.location}</div>
                                    <div style="color: #666; font-size: 14px;">${item.time}</div>
                                    <div style="color: #888; font-size: 13px; margin-top: 4px;">${item.description}</div>
                                </div>
                            `).join('')}
                        </div>

                        <div style="text-align: center; margin-top: 20px;">
                            <button onclick="editTrip('${tripId}')" style="padding: 8px 16px; background: #2196f3; color: white; border: none; border-radius: 20px; margin-right: 8px;">编辑行程</button>
                            <button onclick="shareTrip('${tripId}')" style="padding: 8px 16px; background: #4caf50; color: white; border: none; border-radius: 20px;">分享行程</button>
                        </div>
                    </div>
                `);
            }
        }

        function editTrip(tripId) {
            alert('编辑行程功能开发中...');
        }

        function deleteTrip(tripId) {
            if (confirm('确定要删除这个行程吗？')) {
                alert('行程已删除');
                // 这里可以添加实际的删除逻辑
            }
        }

        function shareTrip(tripId) {
            alert('行程分享链接已复制到剪贴板');
        }

        // 个人中心功能
        function showMyNotes() {
            showModal('我的游记', `
                <div>
                    <div style="display: flex; flex-direction: column; gap: 16px;">
                        <div style="display: flex; background: white; border-radius: 8px; overflow: hidden; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
                            <img src="https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=100" style="width: 80px; height: 80px; object-fit: cover;">
                            <div style="flex: 1; padding: 12px;">
                                <h4>春游骆岗公园</h4>
                                <p style="color: #666; font-size: 14px;">2024年3月25日</p>
                                <div style="display: flex; gap: 12px; font-size: 12px; color: #888;">
                                    <span>👁️ 1,234</span>
                                    <span>❤️ 89</span>
                                </div>
                            </div>
                        </div>

                        <div style="display: flex; background: white; border-radius: 8px; overflow: hidden; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
                            <img src="https://images.unsplash.com/photo-1441974231531-c6227db76b6e?w=100" style="width: 80px; height: 80px; object-fit: cover;">
                            <div style="flex: 1; padding: 12px;">
                                <h4>环巢湖生态之旅</h4>
                                <p style="color: #666; font-size: 14px;">2024年3月20日</p>
                                <div style="display: flex; gap: 12px; font-size: 12px; color: #888;">
                                    <span>👁️ 856</span>
                                    <span>❤️ 67</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div style="text-align: center; margin-top: 20px;">
                        <button onclick="createNewNote()" style="padding: 12px 24px; background: linear-gradient(135deg, #2196f3, #4caf50); color: white; border: none; border-radius: 25px;">
                            ✍️ 写新游记
                        </button>
                    </div>
                </div>
            `);
        }

        function showMyOrders() {
            showModal('我的订单', `
                <div>
                    <div style="display: flex; flex-direction: column; gap: 16px;">
                        <div style="background: white; border-radius: 8px; padding: 16px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
                            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 8px;">
                                <h4>骆岗无人巴士票</h4>
                                <span style="color: #4caf50; font-size: 12px;">已支付</span>
                            </div>
                            <p style="color: #666; font-size: 14px;">订单号：HF202404150001</p>
                            <p style="color: #666; font-size: 14px;">金额：¥20 × 2</p>
                            <div style="text-align: right; margin-top: 8px;">
                                <button onclick="showQRCode()" style="padding: 6px 12px; background: #2196f3; color: white; border: none; border-radius: 15px; font-size: 12px;">
                                    查看二维码
                                </button>
                            </div>
                        </div>

                        <div style="background: white; border-radius: 8px; padding: 16px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
                            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 8px;">
                                <h4>三河古镇门票</h4>
                                <span style="color: #9e9e9e; font-size: 12px;">已使用</span>
                            </div>
                            <p style="color: #666; font-size: 14px;">订单号：HF202403200002</p>
                            <p style="color: #666; font-size: 14px;">金额：¥80 × 2</p>
                        </div>
                    </div>
                </div>
            `);
        }

        function showSettings() {
            showModal('设置', `
                <div>
                    <div style="display: flex; flex-direction: column; gap: 16px;">
                        <div style="display: flex; justify-content: space-between; align-items: center; padding: 16px; background: white; border-radius: 8px;">
                            <span>消息通知</span>
                            <input type="checkbox" checked>
                        </div>

                        <div style="display: flex; justify-content: space-between; align-items: center; padding: 16px; background: white; border-radius: 8px;">
                            <span>位置服务</span>
                            <input type="checkbox" checked>
                        </div>

                        <div style="display: flex; justify-content: space-between; align-items: center; padding: 16px; background: white; border-radius: 8px;">
                            <span>语音播报</span>
                            <input type="checkbox">
                        </div>

                        <div onclick="clearCache()" style="padding: 16px; background: white; border-radius: 8px; text-align: center; cursor: pointer;">
                            清除缓存
                        </div>

                        <div onclick="about()" style="padding: 16px; background: white; border-radius: 8px; text-align: center; cursor: pointer;">
                            关于我们
                        </div>
                    </div>
                </div>
            `);
        }

        // 辅助功能
        function selectRoute(routeType) {
            alert(`您选择了${routeType}路线，正在为您展示详细信息...`);
            closeModal();
        }

        function openAIPlanning() {
            showModal('AI路径规划', `
                <div>
                    <h3 style="text-align: center; margin-bottom: 20px;">🤖 AI智能规划</h3>

                    <div style="display: flex; flex-direction: column; gap: 16px;">
                        <div>
                            <label style="display: block; margin-bottom: 8px; font-weight: 600;">游玩时间</label>
                            <select style="width: 100%; padding: 10px; border: 1px solid #e0e0e0; border-radius: 8px;">
                                <option>1天</option>
                                <option>2天</option>
                                <option>3天</option>
                                <option>4-7天</option>
                            </select>
                        </div>

                        <div>
                            <label style="display: block; margin-bottom: 8px; font-weight: 600;">人数</label>
                            <select style="width: 100%; padding: 10px; border: 1px solid #e0e0e0; border-radius: 8px;">
                                <option>1人</option>
                                <option>2人</option>
                                <option>3-5人</option>
                                <option>6人以上</option>
                            </select>
                        </div>

                        <div>
                            <label style="display: block; margin-bottom: 8px; font-weight: 600;">旅游类型</label>
                            <select style="width: 100%; padding: 10px; border: 1px solid #e0e0e0; border-radius: 8px;">
                                <option>文化历史</option>
                                <option>自然生态</option>
                                <option>亲子游乐</option>
                                <option>康养度假</option>
                                <option>美食体验</option>
                            </select>
                        </div>

                        <div>
                            <label style="display: block; margin-bottom: 8px; font-weight: 600;">预算范围</label>
                            <select style="width: 100%; padding: 10px; border: 1px solid #e0e0e0; border-radius: 8px;">
                                <option>500元以下</option>
                                <option>500-1000元</option>
                                <option>1000-2000元</option>
                                <option>2000元以上</option>
                            </select>
                        </div>

                        <div>
                            <label style="display: block; margin-bottom: 8px; font-weight: 600;">其他需求</label>
                            <textarea placeholder="请描述您的特殊需求..." style="width: 100%; padding: 10px; border: 1px solid #e0e0e0; border-radius: 8px; height: 80px; resize: none;"></textarea>
                        </div>
                    </div>

                    <div style="text-align: center; margin-top: 20px;">
                        <button onclick="generateAIRoute()" style="padding: 12px 24px; background: linear-gradient(135deg, #2196f3, #4caf50); color: white; border: none; border-radius: 25px;">
                            🚀 生成专属路线
                        </button>
                    </div>
                </div>
            `);
        }

        function generateAIRoute() {
            alert('AI正在为您生成专属路线，请稍候...');
            setTimeout(() => {
                alert('路线生成完成！已为您推荐最佳旅游方案。');
                closeModal();
            }, 2000);
        }

        function playAudio(spotName) {
            alert(`正在播放${spotName}的语音介绍...`);
        }

        function startVoiceGuide() {
            alert('语音导览已启动，请说出您想了解的内容...');
        }

        function selectStyle(style) {
            alert(`已选择${style}风格`);
        }

        function generateTravelNote() {
            alert('AI正在为您生成游记，请稍候...');
            setTimeout(() => {
                alert('游记生成完成！');
                closeModal();
            }, 2000);
        }

        function addToItinerary(spotName) {
            alert(`${spotName}已添加到您的行程中`);
        }

        function showRoute(spotName) {
            alert(`正在为您规划到${spotName}的最佳路线...`);
        }

        function bookActivity(activityName) {
            alert(`正在为您预订${activityName}...`);
        }

        function createNewNote() {
            alert('跳转到游记编辑页面...');
        }

        function showQRCode() {
            alert('显示订单二维码');
        }

        function clearCache() {
            alert('缓存已清除');
        }

        function about() {
            showModal('关于我们', `
                <div style="text-align: center;">
                    <h3>合肥文旅助手</h3>
                    <p style="color: #666; margin: 16px 0;">版本 1.0.0</p>
                    <p style="color: #666; margin: 16px 0;">
                        合肥文旅助手是您探索合肥的最佳伙伴，<br>
                        提供智能路径规划、伴游导览、游记生成等服务。
                    </p>
                    <p style="color: #666; margin: 16px 0;">
                        © 2024 合肥文旅助手<br>
                        技术支持：AI智能服务
                    </p>
                </div>
            `);
        }

        // 模态框功能
        function showModal(title, content) {
            document.getElementById('modalTitle').textContent = title;
            document.getElementById('modalBody').innerHTML = content;
            document.getElementById('modal').style.display = 'block';
        }

        function closeModal() {
            document.getElementById('modal').style.display = 'none';
        }

        // 点击模态框外部关闭
        document.addEventListener('DOMContentLoaded', function() {
            const modal = document.getElementById('modal');
            modal.addEventListener('click', function(e) {
                if (e.target === modal) {
                    closeModal();
                }
            });
        });

        // 新增功能函数

        // 跳转到景点详情页面
        function goToAttractionDetail(spotName) {
            // 这里可以跳转到探索页面的景点详情
            switchPage('explore');
            setTimeout(() => {
                showAttractionDetail(spotName);
            }, 300);
        }

        // 选择推荐路线
        function selectRecommendedRoute(routeType) {
            switchPage('route-planning');
            setTimeout(() => {
                showRouteDetail(routeType);
            }, 300);
        }

        // 路径规划页面功能
        function switchRouteMode(mode, element) {
            // 更新按钮状态
            document.querySelectorAll('.float-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            if (element) {
                element.classList.add('active');
            }

            // 切换内容
            document.querySelectorAll('.route-content').forEach(content => {
                content.classList.remove('active');
            });
            document.getElementById(mode + '-routes').classList.add('active');

            currentRouteMode = mode;
        }

        function selectRoute(routeType) {
            // 隐藏推荐路线，显示途经点
            document.getElementById('recommended-routes').classList.remove('active');
            document.getElementById('route-spots-display').classList.add('active');

            // 显示该路线的途经点
            showRouteSpots(routeType);
        }

        function showRouteSpots(routeType) {
            const routes = {
                '亲子游': [
                    {
                        name: '合肥野生动物园',
                        image: 'https://images.unsplash.com/photo-1544551763-46a013bb70d5?w=140&h=80&fit=crop',
                        time: '上午 9:00-12:00',
                        price: '¥120'
                    },
                    {
                        name: '合肥科技馆',
                        image: 'https://images.unsplash.com/photo-1518709268805-4e9042af2176?w=140&h=80&fit=crop',
                        time: '下午 14:00-17:00',
                        price: '¥60'
                    },
                    {
                        name: '融创乐园',
                        image: 'https://images.unsplash.com/photo-1594736797933-d0401ba2fe65?w=140&h=80&fit=crop',
                        time: '第二天 9:00-18:00',
                        price: '¥280'
                    }
                ],
                '文化游': [
                    {
                        name: '三河古镇',
                        image: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=140&h=80&fit=crop',
                        time: '第一天 9:00-17:00',
                        price: '¥80'
                    },
                    {
                        name: '三国遗址公园',
                        image: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=140&h=80&fit=crop',
                        time: '第二天 9:00-12:00',
                        price: '¥40'
                    },
                    {
                        name: '渡江战役纪念馆',
                        image: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=140&h=80&fit=crop',
                        time: '第二天 14:00-17:00',
                        price: '免费'
                    },
                    {
                        name: '包公园',
                        image: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=140&h=80&fit=crop',
                        time: '第三天 9:00-12:00',
                        price: '¥20'
                    }
                ],
                '生态游': [
                    {
                        name: '巢湖湿地公园',
                        image: 'https://images.unsplash.com/photo-1441974231531-c6227db76b6e?w=140&h=80&fit=crop',
                        time: '第一天 9:00-17:00',
                        price: '¥30'
                    },
                    {
                        name: '姥山岛',
                        image: 'https://images.unsplash.com/photo-1441974231531-c6227db76b6e?w=140&h=80&fit=crop',
                        time: '第二天 9:00-16:00',
                        price: '¥80'
                    }
                ],
                '骆岗游': [
                    {
                        name: '骆岗公园',
                        image: 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=140&h=80&fit=crop',
                        time: '全天 9:00-18:00',
                        price: '免费'
                    }
                ],
                '康养游': [
                    {
                        name: '庐江温泉',
                        image: 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=140&h=80&fit=crop',
                        time: '第一天 14:00-22:00',
                        price: '¥280'
                    },
                    {
                        name: '庐江山水',
                        image: 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=140&h=80&fit=crop',
                        time: '第二天 9:00-17:00',
                        price: '¥150'
                    }
                ]
            };

            const spots = routes[routeType] || [];
            const spotsHtml = spots.map((spot, index) => `
                <div class="spot-card" onclick="highlightSpotOnMap('${spot.name}', ${index})">
                    <div class="spot-image" style="background-image: url('${spot.image}')"></div>
                    <div class="spot-info">
                        <h5>${spot.name}</h5>
                        <div class="spot-time">${spot.time}</div>
                        <div class="spot-price">${spot.price}</div>
                    </div>
                </div>
            `).join('');

            document.querySelector('.spots-horizontal-scroll').innerHTML = spotsHtml;
        }

        function highlightSpotOnMap(spotName, index) {
            // 移除之前的高亮
            document.querySelectorAll('.spot-card').forEach(card => {
                card.classList.remove('highlighted');
            });

            // 高亮当前选中的景点
            document.querySelectorAll('.spot-card')[index].classList.add('highlighted');

            // 在地图上显示该景点（这里可以添加地图标记逻辑）
            console.log(`在地图上凸显展示: ${spotName}`);
        }

        function goBackToRoutes() {
            document.getElementById('route-spots-display').classList.remove('active');
            document.getElementById('recommended-routes').classList.add('active');
        }

        function saveCurrentRoute() {
            alert('路线已保存到个人行程');
        }

        // AI规划功能
        function selectOption(category, value, element) {
            // 更新选项状态
            element.parentNode.querySelectorAll('.option-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            element.classList.add('active');

            // 保存选择
            selectedOptions[category] = value;
        }

        function generateAIRoute() {
            const otherRequirements = document.getElementById('otherRequirements').value;

            // 模拟AI生成过程
            const generateBtn = document.querySelector('.generate-btn');
            generateBtn.textContent = '🤖 AI正在规划中...';
            generateBtn.disabled = true;

            setTimeout(() => {
                // 隐藏AI规划表单，显示生成的路线
                document.getElementById('ai-planning-routes').classList.remove('active');
                document.getElementById('ai-generated-route').classList.add('active');

                // 生成路线
                showAIGeneratedRoute();

                generateBtn.textContent = '🚀 生成专属路线';
                generateBtn.disabled = false;
            }, 3000);
        }

        function showAIGeneratedRoute() {
            // 根据选择的参数生成不同的路线
            const generatedSpots = [
                {
                    name: '三河古镇',
                    image: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=160&h=80&fit=crop',
                    time: '上午 9:00-12:00',
                    price: '¥80'
                },
                {
                    name: '包公园',
                    image: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=160&h=80&fit=crop',
                    time: '下午 14:00-17:00',
                    price: '¥20'
                },
                {
                    name: '安徽博物院',
                    image: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=160&h=80&fit=crop',
                    time: '下午 17:30-19:00',
                    price: '免费'
                }
            ];

            const spotsHtml = generatedSpots.map((spot, index) => `
                <div class="generated-spot-card" onclick="highlightGeneratedSpot('${spot.name}', ${index})">
                    <div class="generated-spot-image" style="background-image: url('${spot.image}')"></div>
                    <div class="generated-spot-info">
                        <h5>${spot.name}</h5>
                        <div class="generated-spot-time">${spot.time}</div>
                        <div class="generated-spot-price">${spot.price}</div>
                    </div>
                </div>
            `).join('');

            document.querySelector('.generated-spots-scroll').innerHTML = spotsHtml;

            // 在地图上显示路线
            console.log('在地图上显示AI生成的路线');
        }

        function highlightGeneratedSpot(spotName, index) {
            console.log(`凸显AI生成的景点: ${spotName}`);
        }

        function goBackToAIPlanning() {
            document.getElementById('ai-generated-route').classList.remove('active');
            document.getElementById('ai-planning-routes').classList.add('active');
        }

        function saveAIRoute() {
            alert('AI生成的路线已保存到个人行程');
        }

        function showRouteDetail(routeType) {
            const routes = {
                '亲子游': {
                    title: '两日亲子游路线',
                    summary: '总费用：¥680/人 · 2天 · 适合家庭',
                    spots: [
                        {
                            name: '合肥野生动物园',
                            image: 'https://images.unsplash.com/photo-1544551763-46a013bb70d5?w=150',
                            tags: ['动物观赏', '亲子互动'],
                            price: '¥120'
                        },
                        {
                            name: '合肥科技馆',
                            image: 'https://images.unsplash.com/photo-1518709268805-4e9042af2176?w=150',
                            tags: ['科普教育', '互动体验'],
                            price: '¥60'
                        },
                        {
                            name: '融创乐园',
                            image: 'https://images.unsplash.com/photo-1594736797933-d0401ba2fe65?w=150',
                            tags: ['游乐设施', '娱乐体验'],
                            price: '¥280'
                        }
                    ]
                },
                '文化游': {
                    title: '三天文化游路线',
                    summary: '总费用：¥520/人 · 3天 · 历史文化',
                    spots: [
                        {
                            name: '三河古镇',
                            image: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=150',
                            tags: ['古镇风情', '历史文化'],
                            price: '¥80'
                        },
                        {
                            name: '三国遗址公园',
                            image: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=150',
                            tags: ['历史遗迹', '文化体验'],
                            price: '¥40'
                        },
                        {
                            name: '渡江战役纪念馆',
                            image: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=150',
                            tags: ['红色教育', '历史学习'],
                            price: '免费'
                        },
                        {
                            name: '包公园',
                            image: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=150',
                            tags: ['历史人物', '园林景观'],
                            price: '¥20'
                        }
                    ]
                },
                '生态游': {
                    title: '两天生态休闲路线',
                    summary: '总费用：¥450/人 · 2天 · 生态休闲',
                    spots: [
                        {
                            name: '巢湖湿地公园',
                            image: 'https://images.unsplash.com/photo-1441974231531-c6227db76b6e?w=150',
                            tags: ['湿地生态', '观鸟赏景'],
                            price: '¥30'
                        },
                        {
                            name: '姥山岛',
                            image: 'https://images.unsplash.com/photo-1441974231531-c6227db76b6e?w=150',
                            tags: ['湖心岛屿', '自然风光'],
                            price: '¥80'
                        }
                    ]
                },
                '骆岗游': {
                    title: '一天骆岗游路线',
                    summary: '总费用：¥180/人 · 1天 · 城市公园',
                    spots: [
                        {
                            name: '骆岗公园',
                            image: 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=150',
                            tags: ['城市绿肺', '文化体验'],
                            price: '免费'
                        }
                    ]
                },
                '康养游': {
                    title: '两天康养度假路线',
                    summary: '总费用：¥580/人 · 2天 · 康养度假',
                    spots: [
                        {
                            name: '庐江温泉',
                            image: 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=150',
                            tags: ['温泉养生', '康养度假'],
                            price: '¥280'
                        },
                        {
                            name: '庐江山水',
                            image: 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=150',
                            tags: ['山水风光', '休闲度假'],
                            price: '¥150'
                        }
                    ]
                }
            };

            const route = routes[routeType];
            if (route) {
                // 显示路线详情面板
                document.getElementById('routeTitle').textContent = route.title;
                document.getElementById('routeSummary').innerHTML = `<p>${route.summary}</p>`;

                const spotsHtml = route.spots.map(spot => `
                    <div class="route-spot-card">
                        <div class="spot-card-image" style="background-image: url('${spot.image}')"></div>
                        <div class="spot-card-content">
                            <h5>${spot.name}</h5>
                            <div class="spot-card-tags">
                                ${spot.tags.map(tag => `<span class="spot-tag">${tag}</span>`).join('')}
                            </div>
                            <div class="spot-price">${spot.price}</div>
                        </div>
                    </div>
                `).join('');

                document.getElementById('routeSpots').innerHTML = spotsHtml;
                document.getElementById('routeDetailPanel').classList.add('active');

                // 在地图上显示路线
                displayRouteOnMap(route.spots);
            }
        }

        function closeRouteDetail() {
            document.getElementById('routeDetailPanel').classList.remove('active');
        }

        function displayRouteOnMap(spots) {
            // 这里可以在地图上显示路线点
            console.log('在地图上显示路线:', spots);
        }

        function editRoute() {
            alert('路线编辑功能：可以删除或新增途经点');
        }

        function saveRoute() {
            alert('路线已保存到我的行程');
        }

        function payRoute() {
            alert('跳转到支付页面...');
        }

        // 伴游导览功能
        function toggleVoiceGuide() {
            alert('语音导览已开启');
        }

        function playSpotAudio(spotName) {
            alert(`正在播放${spotName}的语音介绍...`);
            // 模拟景点闪烁效果
            const spot = document.querySelector('.nearby-spot .spot-marker.blinking');
            if (spot) {
                spot.classList.remove('blinking');
                setTimeout(() => spot.classList.add('blinking'), 100);
            }
        }

        function showRouteToSpot(spotName) {
            const routeOptions = `
                <h4>到达 ${spotName} 的路线选择</h4>
                <div class="route-option">
                    <h5>🚶‍♂️ 步行路线</h5>
                    <p>预计30分钟 · 2.5公里</p>
                    <button onclick="selectRoute('walk', '${spotName}')">选择此路线</button>
                </div>
                <div class="route-option">
                    <h5>🚌 公交路线</h5>
                    <p>预计25分钟 · 乘坐126路</p>
                    <button onclick="selectRoute('bus', '${spotName}')">选择此路线</button>
                </div>
                <div class="route-option">
                    <h5>🚗 驾车路线</h5>
                    <p>预计15分钟 · 避开拥堵</p>
                    <button onclick="selectRoute('car', '${spotName}')">选择此路线</button>
                </div>
            `;

            document.getElementById('routeOptions').innerHTML = routeOptions;
            document.getElementById('routeRecommendPanel').classList.add('active');
        }

        function closeRouteRecommend() {
            document.getElementById('routeRecommendPanel').classList.remove('active');
        }

        function selectRoute(type, destination) {
            alert(`已选择${type === 'walk' ? '步行' : type === 'bus' ? '公交' : '驾车'}路线到${destination}`);
            closeRouteRecommend();
        }

        function startVoiceInteraction() {
            document.getElementById('voiceStatus').textContent = '正在聆听...';
            setTimeout(() => {
                document.getElementById('voiceStatus').textContent = '语音识别完成，为您推荐附近餐厅';
            }, 2000);
        }

        function queryNearby(type) {
            alert(`正在查找附近的${type}...`);
        }

        // 伴游导览新功能
        function highlightSpot(spotName) {
            // 移除之前的高亮
            document.querySelectorAll('.attraction-compact').forEach(item => {
                item.classList.remove('highlighted');
            });

            // 高亮当前选中的景点
            event.target.closest('.attraction-compact').classList.add('highlighted');

            // 在地图上凸显该景点
            console.log(`在地图上凸显: ${spotName}`);

            // 播报相关信息
            setTimeout(() => {
                alert(`正在播报${spotName}的相关信息...`);
            }, 500);
        }

        function quickSearch(type) {
            // 在输入框中显示搜索内容
            document.getElementById('searchInput').value = type;

            // 执行搜索
            performSearch();
        }

        function toggleInputMode() {
            const btn = document.getElementById('inputModeBtn');
            const input = document.getElementById('searchInput');

            isVoiceMode = !isVoiceMode;

            if (isVoiceMode) {
                btn.textContent = '⌨️';
                btn.classList.add('voice-mode');
                input.placeholder = '点击麦克风开始语音输入...';
                input.disabled = true;
            } else {
                btn.textContent = '🎤';
                btn.classList.remove('voice-mode');
                input.placeholder = '搜索附近的地点或设施...';
                input.disabled = false;
            }
        }

        function performSearch() {
            const searchTerm = document.getElementById('searchInput').value;

            if (!searchTerm.trim()) {
                alert('请输入搜索内容');
                return;
            }

            if (isVoiceMode) {
                alert(`语音搜索: ${searchTerm}`);
            } else {
                alert(`文字搜索: ${searchTerm}`);
            }

            // 在地图和附近景点中显示搜索结果
            console.log(`搜索结果: ${searchTerm}`);
        }

        // 游记生成功能
        function triggerImageUpload() {
            document.getElementById('imageUpload').click();
        }

        function handleImageUpload(event) {
            const files = event.target.files;
            const preview = document.getElementById('imagePreview');

            for (let i = 0; i < files.length; i++) {
                const file = files[i];
                const reader = new FileReader();

                reader.onload = function(e) {
                    const imageDiv = document.createElement('div');
                    imageDiv.className = 'preview-image';
                    imageDiv.style.backgroundImage = `url('${e.target.result}')`;

                    const removeBtn = document.createElement('button');
                    removeBtn.className = 'remove-image';
                    removeBtn.innerHTML = '×';
                    removeBtn.onclick = function() {
                        imageDiv.remove();
                        uploadedImages = uploadedImages.filter(img => img !== e.target.result);
                    };

                    imageDiv.appendChild(removeBtn);
                    preview.appendChild(imageDiv);

                    uploadedImages.push(e.target.result);
                };

                reader.readAsDataURL(file);
            }
        }

        function selectStyle(style, element) {
            // 更新样式选择
            document.querySelectorAll('.style-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            element.classList.add('active');
            selectedStyle = style;
        }

        function generateTravelNote() {
            const title = document.getElementById('noteTitle').value || '我的合肥之旅';
            const description = document.getElementById('travelDescription').value;
            const tags = document.getElementById('noteTags').value;

            if (!description.trim()) {
                alert('请先输入文字描述');
                return;
            }

            // 模拟AI生成过程
            const generateBtn = document.querySelector('.generate-note-btn');
            generateBtn.textContent = '🤖 AI正在生成中...';
            generateBtn.disabled = true;

            setTimeout(() => {
                const generatedContent = generateNoteContent(title, description, selectedStyle, tags);
                showGeneratedNote(generatedContent);

                generateBtn.textContent = '✨ AI生成游记';
                generateBtn.disabled = false;
            }, 3000);
        }

        function generateNoteContent(title, description, style, tags) {
            const styleTemplates = {
                '文艺': {
                    opening: '时光荏苒，回想起那段在合肥的美好时光，心中满怀温暖。',
                    tone: '诗意盎然，情感丰富'
                },
                '实用': {
                    opening: '合肥旅游实用攻略，为你的出行提供详细指南。',
                    tone: '条理清晰，信息详实'
                },
                '趣味': {
                    opening: '哈哈，合肥之旅真是太有趣了！让我来跟大家分享一下这次的奇妙经历。',
                    tone: '轻松幽默，生动有趣'
                },
                '简约': {
                    opening: '合肥，一座值得细细品味的城市。',
                    tone: '简洁明了，重点突出'
                }
            };

            const template = styleTemplates[style] || styleTemplates['文艺'];

            return `
                <div class="note-header">
                    <h2>${title}</h2>
                    <div class="note-meta">
                        <span>📅 ${new Date().toLocaleDateString()}</span>
                        <span>🏷️ ${tags || '合肥旅游'}</span>
                        <span>✍️ ${style}风格</span>
                    </div>
                </div>

                <div class="note-content">
                    <p class="note-opening">${template.opening}</p>

                    <div class="note-images">
                        ${uploadedImages.map(img => `<img src="${img}" alt="旅行照片" style="width: 100%; margin: 10px 0; border-radius: 8px;">`).join('')}
                    </div>

                    <div class="note-body">
                        <p>${description}</p>

                        <p>这次的合肥之旅让我深深感受到了这座城市的魅力。从古色古香的三河古镇到现代化的科技馆，从宁静的骆岗公园到波光粼粼的巢湖，每一处风景都让人流连忘返。</p>

                        <p>特别推荐大家一定要去骆岗公园走走，那里不仅有美丽的自然风光，还有丰富的文化内涵。而环巢湖的生态之美更是让人心旷神怡，是放松身心的绝佳去处。</p>
                    </div>

                    <div class="note-footer">
                        <p><em>愿每一次旅行都能带给我们美好的回忆，愿每一座城市都能留下我们青春的足迹。</em></p>
                    </div>
                </div>

                <div class="note-actions">
                    <button onclick="editGeneratedNote()">✏️ 修改游记</button>
                    <button onclick="saveGeneratedNote()">💾 保存游记</button>
                    <button onclick="shareGeneratedNote()">📤 分享游记</button>
                </div>
            `;
        }

        function showGeneratedNote(content) {
            const noteDiv = document.getElementById('generatedNote');
            noteDiv.innerHTML = content;
            noteDiv.classList.add('active');

            // 滚动到生成的游记
            noteDiv.scrollIntoView({ behavior: 'smooth' });
        }

        function previewNote() {
            const title = document.getElementById('noteTitle').value || '我的合肥之旅';
            const description = document.getElementById('travelDescription').value;

            if (!description.trim()) {
                alert('请先输入文字描述');
                return;
            }

            const previewContent = `
                <h3>游记预览</h3>
                <h4>${title}</h4>
                <div style="margin: 10px 0;">
                    ${uploadedImages.map(img => `<img src="${img}" style="width: 80px; height: 80px; object-fit: cover; margin: 2px; border-radius: 4px;">`).join('')}
                </div>
                <p>${description}</p>
                <p><strong>风格：</strong>${selectedStyle}</p>
            `;

            showModal('游记预览', previewContent);
        }

        function editGeneratedNote() {
            alert('进入游记编辑模式...');
        }

        function saveGeneratedNote() {
            alert('游记已保存到我的游记');
        }

        function shareGeneratedNote() {
            alert('游记分享链接已生成');
        }

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('合肥文旅助手已加载完成');
        });
    </script>
</body>
</html>
